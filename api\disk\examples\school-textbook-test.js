/**
 * 校本教辅功能测试脚本
 */

const axios = require('axios')

// 配置
const config = {
  baseURL: 'http://localhost:1337',
  token: 'your-jwt-token-here', // 替换为实际的JWT token
}

// 创建axios实例
const api = axios.create({
  baseURL: config.baseURL,
  headers: {
    'Authorization': `Bearer ${config.token}`,
    'Content-Type': 'application/json'
  }
})

// 测试数据
const testTextbook = {
  name: '三年级数学练习册',
  description: '适合三年级学生的数学练习，包含基础运算、应用题等内容',
  grade: '三年级',
  period: '小学',
  subject: '数学',
  category: '练习册',
  tags: ['数学', '练习', '三年级', '基础运算'],
  status: 'draft',
  remark: '这是一个测试教辅'
}

async function testSchoolTextbook() {
  console.log('🧪 校本教辅功能测试')
  console.log('=' .repeat(60))

  try {
    // 1. 创建校本教辅
    console.log('📝 1. 创建校本教辅...')
    const createResponse = await api.post('/school-textbooks/actions/create', testTextbook)
    const textbookId = createResponse.data.data.id
    console.log(`   ✅ 创建成功，ID: ${textbookId}`)
    console.log(`   📋 名称: ${createResponse.data.data.name}`)
    console.log(`   📚 年级: ${createResponse.data.data.grade}`)
    console.log(`   🏫 学段: ${createResponse.data.data.period}`)
    console.log(`   📖 科目: ${createResponse.data.data.subject}`)

    // 2. 获取校本教辅详情
    console.log('\n📖 2. 获取校本教辅详情...')
    const getResponse = await api.get(`/school-textbooks/${textbookId}`)
    console.log(`   ✅ 获取成功`)
    console.log(`   📊 状态: ${getResponse.data.data.status}`)
    console.log(`   📁 文件数量: ${getResponse.data.data.file_count}`)
    console.log(`   💾 总大小: ${getResponse.data.data.total_size} bytes`)

    // 3. 更新校本教辅
    console.log('\n✏️ 3. 更新校本教辅...')
    const updateData = {
      description: '更新后的描述：适合三年级学生的数学练习，内容更加丰富',
      tags: ['数学', '练习', '三年级', '基础运算', '更新'],
      is_featured: true
    }
    const updateResponse = await api.put(`/school-textbooks/actions/${textbookId}/update`, updateData)
    console.log(`   ✅ 更新成功`)
    console.log(`   📝 新描述: ${updateResponse.data.data.description.substring(0, 50)}...`)
    console.log(`   ⭐ 推荐状态: ${updateResponse.data.data.is_featured}`)

    // 4. 发布校本教辅
    console.log('\n🚀 4. 发布校本教辅...')
    const publishResponse = await api.post('/school-textbooks/actions/batch', {
      ids: [textbookId],
      operation: 'publish'
    })
    console.log(`   ✅ 发布成功`)
    console.log(`   📊 操作结果: ${publishResponse.data.data.operation}`)
    console.log(`   🔢 影响数量: ${publishResponse.data.data.updated_count}`)

    // 5. 增加浏览次数
    console.log('\n👀 5. 增加浏览次数...')
    for (let i = 0; i < 3; i++) {
      const viewResponse = await api.post(`/school-textbooks/actions/${textbookId}/view`)
      console.log(`   👁️ 浏览次数: ${viewResponse.data.data.view_times}`)
    }

    // 6. 增加下载次数
    console.log('\n⬇️ 6. 增加下载次数...')
    for (let i = 0; i < 2; i++) {
      const downloadResponse = await api.post(`/school-textbooks/actions/${textbookId}/download`)
      console.log(`   📥 下载次数: ${downloadResponse.data.data.download_times}`)
    }

    // 7. 获取校本教辅列表
    console.log('\n📋 7. 获取校本教辅列表...')
    const listResponse = await api.get('/school-textbooks?status=published&page=1&pageSize=5')
    console.log(`   ✅ 获取成功`)
    console.log(`   📊 总数: ${listResponse.data.pagination.total}`)
    console.log(`   📄 当前页: ${listResponse.data.pagination.page}`)
    console.log(`   📚 教辅列表:`)
    listResponse.data.data.forEach((item, index) => {
      console.log(`     ${index + 1}. ${item.name} (${item.grade} ${item.subject})`)
    })

    // 8. 搜索校本教辅
    console.log('\n🔍 8. 搜索校本教辅...')
    const searchResponse = await api.get('/school-textbooks?search=数学&grade=三年级')
    console.log(`   ✅ 搜索成功`)
    console.log(`   🔍 搜索关键词: 数学`)
    console.log(`   📊 搜索结果: ${searchResponse.data.data.length} 条`)

    // 9. 设置为推荐
    console.log('\n⭐ 9. 设置为推荐...')
    const featureResponse = await api.post('/school-textbooks/actions/batch', {
      ids: [textbookId],
      operation: 'feature'
    })
    console.log(`   ✅ 设置推荐成功`)

    // 10. 获取推荐教辅
    console.log('\n🌟 10. 获取推荐教辅...')
    const featuredResponse = await api.get('/school-textbooks?is_featured=true&status=published')
    console.log(`   ✅ 获取推荐教辅成功`)
    console.log(`   ⭐ 推荐教辅数量: ${featuredResponse.data.data.length}`)

    // 11. 归档校本教辅
    console.log('\n📦 11. 归档校本教辅...')
    const archiveResponse = await api.post('/school-textbooks/actions/batch', {
      ids: [textbookId],
      operation: 'archive'
    })
    console.log(`   ✅ 归档成功`)

    // 12. 删除校本教辅
    console.log('\n🗑️ 12. 删除校本教辅...')
    const deleteResponse = await api.delete(`/school-textbooks/${textbookId}`)
    console.log(`   ✅ 删除成功`)

    console.log('\n🎉 所有测试完成!')
    console.log('=' .repeat(60))

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    
    if (error.response) {
      console.log(`   HTTP状态: ${error.response.status}`)
      console.log(`   错误详情:`, error.response.data)
    }
  }
}

// 测试数据验证
async function testDataValidation() {
  console.log('\n🔍 数据验证测试')
  console.log('=' .repeat(60))

  const invalidCases = [
    {
      name: '缺少必填字段',
      data: { name: '测试教辅' }, // 缺少grade, period, subject
    },
    {
      name: '无效的年级',
      data: { ...testTextbook, grade: '无效年级' },
    },
    {
      name: '无效的学段',
      data: { ...testTextbook, period: '无效学段' },
    },
    {
      name: '无效的状态',
      data: { ...testTextbook, status: 'invalid_status' },
    },
    {
      name: '名称过长',
      data: { ...testTextbook, name: 'a'.repeat(201) },
    }
  ]

  for (const testCase of invalidCases) {
    try {
      console.log(`\n🧪 测试: ${testCase.name}`)
      await api.post('/school-textbooks/actions/create', testCase.data)
      console.log(`   ❌ 应该失败但成功了`)
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log(`   ✅ 正确拒绝: ${error.response.data.message}`)
      } else {
        console.log(`   ❓ 意外错误: ${error.message}`)
      }
    }
  }
}

// 性能测试
async function testPerformance() {
  console.log('\n⚡ 性能测试')
  console.log('=' .repeat(60))

  // 批量创建测试
  console.log('\n📊 批量创建测试...')
  const startTime = Date.now()
  const createPromises = []

  for (let i = 0; i < 5; i++) {
    const data = {
      ...testTextbook,
      name: `性能测试教辅 ${i + 1}`,
      description: `这是第 ${i + 1} 个性能测试教辅`
    }
    createPromises.push(api.post('/school-textbooks/actions/create', data))
  }

  try {
    const results = await Promise.all(createPromises)
    const endTime = Date.now()
    const duration = endTime - startTime

    console.log(`   ✅ 批量创建完成`)
    console.log(`   📊 创建数量: ${results.length}`)
    console.log(`   ⏱️ 耗时: ${duration}ms`)
    console.log(`   📈 平均耗时: ${(duration / results.length).toFixed(2)}ms/个`)

    // 清理测试数据
    const ids = results.map(r => r.data.data.id)
    await api.post('/school-textbooks/actions/batch', {
      ids: ids,
      operation: 'delete'
    })
    console.log(`   🧹 清理测试数据完成`)

  } catch (error) {
    console.error(`   ❌ 批量创建失败: ${error.message}`)
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🎯 校本教辅完整功能测试')
  console.log('=' .repeat(80))
  console.log(`📡 服务器: ${config.baseURL}`)
  console.log(`🔑 Token: ${config.token.substring(0, 20)}...`)
  console.log('')

  // 基础功能测试
  await testSchoolTextbook()

  // 数据验证测试
  await testDataValidation()

  // 性能测试
  await testPerformance()

  console.log('\n🎉 所有测试完成!')
  console.log('=' .repeat(80))
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = {
  testSchoolTextbook,
  testDataValidation,
  testPerformance
}
