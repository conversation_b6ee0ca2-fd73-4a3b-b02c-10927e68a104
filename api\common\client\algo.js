/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const axios = require('axios');
const logger = strapi.log;
const ALGO_SERVER = strapi.config.server.algoApi;
const ai_ques_server = strapi.config.server.aiQuesApi;


module.exports = {
    enhancePaper,
    recommendPaper,
    levelPaper,
    detailTablePaper,
    mergeFile,
    recommendQuestions,
    knowledgesPaper,
    getRecoQuestions,
};

/**
 * 考后巩固组卷
 * @param params
 * @returns {Promise<*>}
 */
async function enhancePaper (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/past_exam/enhance_paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);

    if (!result.data || result.data.code !== 0 || !result.data.data) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


/**
 * 教师计划试卷推荐
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=29688486
 * @param params
 * @returns {Promise<unknown>}
 */
async function recommendPaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/organize_paper',
        port: ALGO_SERVER.port,
    });
    let options = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, options);
    if (!result.data || result.data.code !== 0 || !result.data.data) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`);
        return null;
    }
    return result;
}

/**
 * 考后巩固组卷（简单卷，中等卷，培优卷）
 * @param params
 * @returns {Promise<*>}
 */
async function levelPaper (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/past_exam/level_paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0 || !result.data.data) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


/**
 * 薄弱知识点组卷
 * @param params
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function detailTablePaper(params) {
    let url = URL.format({
        host: ALGO_SERVER.url,
        pathname: '/detail_table/paper'
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function mergeFile(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/files/process',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function recommendQuestions(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/questions',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function knowledgesPaper(params) {
    let url = URL.format({
        host: ALGO_SERVER.url,
        pathname: '/knowledges/paper',
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法知识点组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function getRecoQuestions(questionId) {
    let url = URL.format({
        protocol: ai_ques_server.protocol,
        hostname: ai_ques_server.hostname,
        pathname: `/sim_ques/v1/recom_sim_ques`
    })
    const params = {
        sam_kb_ques_id: questionId
    }
    let options = { headers: {}, timeout: 50000 };
    try {
        const result = await axios.get(url, { params: params }, options);
        if (result.status === 200) return result.data.data.reco_sim_questions;
        return [];
    } catch (e) {
        logger.error(e);
        return [];
    }
}

