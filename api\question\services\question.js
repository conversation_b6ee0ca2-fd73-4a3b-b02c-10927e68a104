const _ = require('lodash')
const axios = require('axios');
const client = require('../../common/client');
const enums = require('../../common/enums');
const utils = require('../../common/lib/utils');
const studyService = require('../../common/services/study');
const MODEL = 'question';
const MODEL_KNOWLEDGE = 'knowledge';

module.exports = {
  getByIds,
  syncPaperQuestions,
  syncQuestionKnowledge,
  syncPaperQuestionRef,
  syncPaperQuestionShareStatus,
}

async function getByIds(user, ids) {
  const list = await strapi.query(MODEL).find({id_in: ids});
  if (_.size(list)) {
    list.forEach(e => {
      e.period = e.period.name;
      e.subject = e.subject.name;
      e.user = utils.pickFields(e.user, ['id', 'username'])
      e.pBranch = utils.pickFields(e.pBranch, ['id', 'name'])
      delete e.deleted;
      delete e.source;
      delete e.source_id;
    });
  }
  return list;
}

/**
 * 根据试卷同步试题到学科空间
 * @param user
 * @param paper
 * @returns {Promise<void>}
 */
async function syncPaperQuestions(user, paper) {
  if (paper.status === enums.PaperStatus.DONE) return; //
  const questions = [];
  for (const volume of paper.volumes) {
    for (const block of volume.blocks) {
      questions.push(...block.questions);
    }
  }
  if (!_.size(questions)) return;
  //
  const kb_ids = _.chain(questions).filter(e => e.source === enums.QuestionSource.SYS).map(e => e.id).value();
  let kb_questions = [];
  let kbQuesMap = {}
  if (_.size(kb_ids)) {
    kb_questions = await client.kb.getQuestionByIds(kb_ids);
    kbQuesMap = _.keyBy(kb_questions, 'id');
  }
  const {to_year} = await studyService.getAcademicYear();
  // 同步试题
  for (const volume of paper.volumes) {
    for (const block of volume.blocks) {
      for (const index in block.questions) {
        const ques = block.questions[index];
        if (ques.source === enums.QuestionSource.JY) {
          block.questions[index] = {id: ques.id, source: enums.QuestionSource.JY, source_id: ques.id };
          continue;
        }
        const knowledgeQuestion = ques.source === enums.QuestionSource.SYS ? kbQuesMap[ques.source_id] : ques;
        knowledgeQuestion.period = paper.period;
        knowledgeQuestion.subject = paper.subject;
        const { knowledges, blockKnowledges } = await syncQuestionKnowledge(user,  knowledgeQuestion);
        let newQues = {
          type: ques.type,
          period: paper.period,
          subject: paper.subject,
          grade: paper.grade,
          difficulty: knowledgeQuestion.difficulty || 3, // 默认难度
          score: knowledgeQuestion.score,
          description: knowledgeQuestion.description || '',
          comment: knowledgeQuestion.comment || '',
          year: paper.to_year || to_year,
          knowledges: knowledges,
          blocks: knowledgeQuestion.blocks,
          audio: knowledgeQuestion.audio,
          refer_exampapers: [],
          source: ques.source,
          source_id: ques.source === enums.QuestionSource.SYS ? knowledgeQuestion.id.toString() : enums.QuestionSource.UPLOAD,
          shared: paper.shared ? enums.Bool.YES : enums.Bool.NO,
          user: user.id,
          pBranch: user.pBranch.id
        };
        newQues.blocks.knowledges = blockKnowledges;
        newQues = await strapi.query(MODEL).create(newQues);
        block.questions[index] = { id: newQues.id, source: enums.QuestionSource.JY, source_id: newQues.id, score: ques.score, type: ques.type };
      }
    }
  }
}

/**
 * 处理试卷引用试题
 * @param user
 * @param paper
 * @returns {Promise<void>}
 */
async function syncPaperQuestionRef(user, paper) {
  if (_.isEmpty(paper)) return;
  //
  const questions = [];
  for (const volume of paper.volumes) {
    for (const block of volume.blocks) {
      questions.push(...block.questions);
    }
  }
  if (!_.size(questions)) return;
  const ids = questions.map(e => e.id);
  const questionList = await strapi.query(MODEL).find({id_in: ids});
  const refInfo = {
    id: paper.id, // 试卷ID
    name: paper.name, // 名称
    year: paper.to_year, // 学年
    from_year: paper.from_year, // 学年
    category: paper.type, // 试卷类型
    province: paper.province, // 省
    city: paper.city, // 市
    region: paper.region, // 区
    grade: paper.grade // 年级
  };
  for (const ques of questionList) {
    const ref = (ques.refer_exampapers || []).find(e => e.id === refInfo.id);
    if (!_.isEmpty(ref)) continue;
    const refer_exampapers = ques.refer_exampapers || [];
    refer_exampapers.push(refInfo);
    await strapi.query(MODEL).update({id: ques.id}, {refer_exampapers, refer_times: ques.refer_times + 1});
  }
}

async function syncQuestionKnowledge (user, question) {
  const result = {
    knowledges: [], // 试题知识点
    blockKnowledges: [] // 小题知识点
  }
  if (!_.size(question.knowledges)) return result; // 没有知识点
  const list = await strapi.query(MODEL_KNOWLEDGE).find({ pBranch: user.pBranch.id, source_id_in: question.knowledges.map(e => Number(e.id))});
  const knowledges = [];
  for (const know of question.knowledges) {
    let docKnow = list.find(e => e.source_id === Number(know.id));
    if (_.isEmpty(docKnow)) {
      docKnow = {
        name: know.name,
        key: `${know.name}-${question.period}-${question.subject}`,
        period: question.period,
        subject: question.subject,
        source_id: Number(know.id),
        source: enums.KnowledgeSyncType.SYNC,
        operator: user.id,
        creator: user.id,
        pBranch: user.pBranch.id
      };
      docKnow = await strapi.query(MODEL_KNOWLEDGE).create(docKnow);
    }
    knowledges.push({
      id: docKnow.id,
      name: docKnow.name,
    });
  }
  result.knowledges = knowledges;
  // 处理小题知识点
  if (_.size(question.blocks.knowledges)) {
    for (const index in (question.blocks.knowledges || [])) {
      const stemKnowledge = question.blocks.knowledges[index];
      const arr = [];
      if (_.size(stemKnowledge)) {
        for (const know of stemKnowledge) {
          const tempKnow = knowledges.find(e => e.name === know.name);
          arr.push({
            id:tempKnow.id,
            name: tempKnow.name
          });
        }
      }
      result.blockKnowledges.push(arr);
    }
  }
  return result;
}

/**
 * 同步试卷试题共享状态
 * @param user
 * @param paper
 * @returns {Promise<void>}
 */
async function syncPaperQuestionShareStatus(user, paper) {
  if (_.isEmpty(paper)
    || paper.status !== enums.PaperStatus.DONE
    || !paper.shared
    || paper.source === enums.PaperSourceType.REF
  ) return;
  const ids = [];
  for (const volume of paper.volumes) {
    for (const block of volume.blocks) {
      ids.push(...block.questions.map(e => e.id));
    }
  }
  const list = await strapi.query(MODEL).find({id_in: ids, pBranch: user.pBranch.id, shared: enums.Bool.NO});
  if (!_.size(list)) return;
  for (const data of list) {
    await strapi.query(MODEL).update({id: data.id}, {shared: enums.Bool.YES})
  }
}
