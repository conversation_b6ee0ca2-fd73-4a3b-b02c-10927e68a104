const { createDefaultRoutes } = require('accel-utils')
module.exports = {
  'routes': [
    // 下载记录
    ...createDefaultRoutes({
      basePath: '/download-records',
      controller: 'download-record'
    }),
    {
      'method': 'GET',
      'path': '/download-records/actions/getList',
      'handler': 'download-record.getList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/download-records/actions/:id/deleteById',
      'handler': 'download-record.deleteById',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 结束
    // 阅卷UnifyToken登录
    {
      path: `/auth/actions/loginByUnifyToken`,
      method: 'POST',
      handler: `auth.loginByUnifyToken`,
      config: {
        policies: [],
      }
    },
    // 获取登录信息
    {
      path: `/user/actions/getInfo`,
      method: 'GET',
      handler: `user.getInfo`,
      config: {
        policies: [],
      }
    },
  ]
}
