'use strict'
const { BranchCurdRouter } = require('accel-utils')
const { ObjectId } = require('mongodb')
const axios = require('axios')
const _ = require('lodash')

const branchCurdRouter = new (class extends BranchCurdRouter {
  async branchUpdate (ctx) {
    const { params, data } = this._parseBranchCtx(ctx)
    const userId = ctx.state.user.id
    if (data.name) {
      const version = await strapi.query('press-version').findOne({ id: params.id, }, [])
      data.key = `${data.name}-${version.subject}`
    }
    data.operator = userId
    return super.branchUpdate(ctx)
  }
})('press-version')

// 异步获取学校教材版本目录
async function getPressVersionCatalogByBranch (ctx) {
  const { subjectId } = ctx.request.query
  if (!subjectId) return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  const branchId = ctx.state.user.pBranch?.id
  const subject = await strapi.query('subject').findOne({
    pBranch: branchId,
    id: subjectId,
    _sort: 'name:ASC',
    _limit: -1,
  }, ['press_versions'])
  if (!subject) return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  const pressVersionIds = _.flatten(subject.press_versions.map(e => e.id))

  const pressVersions = await strapi.query('press-version').find({
    id_in: pressVersionIds,
    subject: subjectId,
    _limit: -1,
  }, ['books'])

  // 删除学校科目下 异常教材版本
  await strapi.query('press-version').model.deleteMany({
    _id: { $nin: pressVersionIds },
    subject: subjectId,
  })
  const bookIds = _.flatten(_.flatten(subject.press_versions.map(pressVersion => pressVersion.books)))
  // 删除校科目下 异常教材
  await strapi.query('book').model.deleteMany({
    _id: { $nin: bookIds },
    subject: subjectId,
  })

  // 初始化返回对象，用于组织教材目录信息
  let returnObj = {
    book: {
      children: [],
    }
  }
  // 遍历每个学期，构建学期对象，并将其添加到返回对象中
  for (let pressVersion of subject.press_versions) {
    pressVersion = pressVersions.find(item => item.id === pressVersion.id)
    let pressVersionObj = {
      key: 'press_version',
      name: pressVersion.name,
      id: pressVersion.id,
      source: pressVersion.source,
      source_id: pressVersion.source_id,
      children: [],
    }
    // 遍历教材版本中的每本教材，构建教材对象，并将其添加到当前教材版本对象中
    for (const book of pressVersion.books) {
      let bookObj = {
        key: 'press_volume',
        name: book.name,
        id: book.id,
        source: book.source,
        source_id: book.source_id,
      }
      pressVersionObj.children.push(bookObj)
    }
    returnObj.book.children.push(pressVersionObj)
  }

  // 返回组织好的教材目录信息
  return ctx.wrapper.succ(returnObj)
}


// 同步新增本校教材版本
async function syncPressVersionCatalogByBranch (ctx) {
  const { subjectId, book } = ctx.request.body
  const branchId = ctx.state.user.pBranch?.id
  const userId = ctx.state.user.id
  if (!branchId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户异常')
  }

  // 验证输入数据
  if (!book || !Array.isArray(book.children) || !subjectId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '无效的输入数据')
  }
  try {
    // 遍历整个目录 并创建新节点和更新关联次序

    const subject = await strapi.query('subject').findOne({
      pBranch: branchId,
      id: subjectId,
      _sort: 'name:ASC',
      _limit: -1,
    }, ['press_versions'])
    if (!subject) return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    let pressVersions = []
    let versionInsertArray = [], bookInsertArray = [], bookChapterInsertArray = []
    let subjectBulkWriteArray = [], versionBulkWriteArray = []
   
    for (let version of book?.children || []) {
      if (!version.id) {
        const newPressVersion =  {
          _id: new ObjectId().toString(),
          name: version.name,
          source: version.source,
          subject: subject.id,
          period: subject.period,
          key: `${version.name}-${subject.id}`,
          operator: userId,
          creator: userId,
          pBranch: branchId,
        }
        version.id = newPressVersion._id
        versionInsertArray.push(newPressVersion)
      }
      pressVersions.push(version.id)
      let books = []
      for (let curBook of version?.children || []) {
        if (!curBook.id) {
          const bookChapterId = new ObjectId().toString()
          // 创建book和顶层章节
          const newBook = {
            _id: new ObjectId().toString(),
            name: curBook.name,
            source: curBook.source,
            source_id: curBook.source_id,
            press_version: version.id,
            period: subject.period,
            subject: subject.id,
            key: `${curBook.name}-${version.id}`,
            operator: userId,
            creator: userId,
            pBranch: branchId,
            book_chapter: bookChapterId,
            children: [],
          }
          bookInsertArray.push(newBook)
          curBook.id = newBook._id

          const newChapter = {
            _id: bookChapterId,
            book: curBook.id,
            name: curBook.name,
            path: `${subject.period}-${subject.id}-${version.id}-${bookChapterId}`,
            // key: `${subject.period}-${subject.id}-${version.id}-${curBook.name}`,
            period: subject.period,
            subject: subject.id,
            source: curBook.source,
            source_id: curBook.source_id,
            press_version: version.id,
            operator: userId,
            creator: userId,
            pBranch: branchId,
          }
          bookChapterInsertArray.push(newChapter)
        }
        books.push(curBook.id)
      }
      // 更新教材版本，关联教材分册和次序
      if (books.length > 0) {
        versionBulkWriteArray.push({
          updateOne: {
            filter: { _id: version.id },
            update: { $set: { books: books } },
          },
        })
      }
      // await strapi.query('press-version').update({ id: version.id, }, { books: books, })
    }
    // 更新学科，关联教材版本和次序
    if (pressVersions.length > 0) {
      subjectBulkWriteArray.push({
        updateOne: {
          filter: { _id: subject.id },
          update: { $set: { press_versions: pressVersions } },
        },
      })
    }
    // await strapi.query('subject').update({ id: subject.id, }, { press_versions: pressVersions, })
    
    // 批量插入或更新
    if (versionInsertArray.length > 0) {
      let result = await strapi.query('press-version').model.insertMany(versionInsertArray)
      // console.log(result)
    }
    if (bookInsertArray.length > 0) {
      let result = await strapi.query('book').model.insertMany(bookInsertArray)
      // console.log(result)
    }
    if (bookChapterInsertArray.length > 0) {
      let result = await strapi.query('book-chapter').model.insertMany(bookChapterInsertArray)
      // console.log(result)
    }
    if (subjectBulkWriteArray.length > 0) {
      let result = await strapi.query('subject').model.bulkWrite(subjectBulkWriteArray)
      // console.log(result)
    }
    if (versionBulkWriteArray.length > 0) {
      let result = await strapi.query('press-version').model.bulkWrite(versionBulkWriteArray)
      // console.log(result)
    }

    return ctx.wrapper.succ({ book })
  } catch (e) {
    if (e.message === 'Duplicate entry') {
      console.error(e) // 记录错误日志
      return ctx.wrapper.error('HANDLE_ERROR', '数据重复', { book })
    }
    console.error('Error syncing book catalog:', e) // 记录错误日志
    throw e
  }
}


module.exports = {
  getPressVersionCatalogByBranch,
  syncPressVersionCatalogByBranch,
  ...branchCurdRouter.createHandlers(),
}