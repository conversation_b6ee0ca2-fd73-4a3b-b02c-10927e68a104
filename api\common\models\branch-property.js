module.exports = {
  collectionName: 'branch-property',
  info: {
    name: 'branch-property',
    label: '学校属性',
    description: '学校属性'
  },
  options: {
    timestamps: true,
    indexes: [
      // { keys: { key: 1 }, options: { unique: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      type: 'string',
      required: true,
    },
    key: {
      label: '键',
      type: 'string',
      required: true,
    },
    children: {
      label: '值',
      type: 'json',
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '创建人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0
    }
  }
}
