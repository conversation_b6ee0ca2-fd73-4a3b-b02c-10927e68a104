'use strict'

const axios = require('axios')
const path = require('path')

/**
 * 从URL下载文件到内存
 * @param {string} url - 文件URL
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<Buffer>} 文件内容Buffer
 */
async function downloadFileFromUrl(url, timeout = 30000) {
  try {
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      timeout: timeout,
      maxContentLength: 100 * 1024 * 1024, // 100MB限制
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    return Buffer.from(response.data)
  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      throw new Error(`下载超时: ${url}`)
    } else if (error.response) {
      throw new Error(`下载失败 (${error.response.status}): ${url}`)
    } else {
      throw new Error(`网络错误: ${error.message}`)
    }
  }
}

/**
 * 生成安全的文件名
 * @param {string} originalName - 原始文件名
 * @param {number} index - 文件索引
 * @returns {string} 安全的文件名
 */
function generateSafeFilename(originalName, index) {
  if (!originalName) {
    return `file_${index}.bin`
  }

  // 移除危险字符，保留中文、英文、数字、点、下划线、连字符
  let safeName = originalName.replace(/[<>:"/\\|?*\x00-\x1f]/g, '_')
  
  // 限制文件名长度
  if (safeName.length > 200) {
    const ext = path.extname(safeName)
    const nameWithoutExt = path.basename(safeName, ext)
    safeName = nameWithoutExt.substring(0, 200 - ext.length) + ext
  }

  // 如果文件名为空或只有扩展名，使用默认名称
  if (!safeName || safeName.startsWith('.')) {
    const ext = path.extname(originalName) || '.bin'
    safeName = `file_${index}${ext}`
  }

  return safeName
}

/**
 * 批量下载文件
 * @param {Array} files - 文件列表
 * @param {number} timeout - 超时时间
 * @param {number} concurrencyLimit - 并发限制
 * @returns {Promise<object>} 下载结果 {success: [], failed: []}
 */
async function batchDownloadFiles(files, timeout = 30000, concurrencyLimit = 5) {
  const results = {
    success: [],
    failed: []
  }

  console.log(`开始批量下载 ${files.length} 个文件，并发限制: ${concurrencyLimit}`)

  // 分批处理，控制并发数
  for (let i = 0; i < files.length; i += concurrencyLimit) {
    const batch = files.slice(i, i + concurrencyLimit)
    const batchPromises = batch.map(async (file, batchIndex) => {
      const actualIndex = i + batchIndex

      try {
        console.log(`开始下载文件 ${actualIndex + 1}/${files.length}: ${file.name}`)

        // 下载文件
        const fileBuffer = await downloadFileFromUrl(file.url, timeout)

        // 生成文件名，优先使用原始文件名
        const filename = generateSafeFilename(file.name, actualIndex + 1)

        results.success.push({
          id: file.id,
          name: file.name,
          filename: filename,
          buffer: fileBuffer,
          size: fileBuffer.length,
          url: file.url
        })

        console.log(`文件下载成功: ${filename} (${fileBuffer.length} bytes)`)

      } catch (error) {
        console.error(`文件下载失败 ${actualIndex + 1}:`, error.message)
        results.failed.push({
          id: file.id,
          name: file.name,
          url: file.url,
          error: error.message
        })
      }
    })

    // 等待当前批次完成再处理下一批次
    await Promise.all(batchPromises)
  }

  console.log(`批量下载完成: 成功 ${results.success.length}/${files.length} 个文件`)
  return results
}

/**
 * 创建下载结果摘要
 * @param {object} task - 任务对象
 * @param {Array} validFiles - 有效文件列表
 * @param {object} results - 下载结果
 * @returns {object} 摘要对象
 */
function createDownloadSummary(task, validFiles, results) {
  // 移除buffer字段避免JSON过大
  const summaryFiles = results.success.map(file => ({
    id: file.id,
    name: file.name,
    filename: file.filename,
    size: file.size,
    url: file.url
  }))

  return {
    taskId: task.id,
    taskName: task.name,
    taskType: task.task_type,
    downloadTime: new Date().toISOString(),
    totalFiles: validFiles.length,
    successCount: results.success.length,
    failedCount: results.failed.length,
    successFiles: summaryFiles,
    failedFiles: results.failed
  }
}

module.exports = {
  downloadFileFromUrl,
  generateSafeFilename,
  batchDownloadFiles,
  createDownloadSummary
}
