'use strict'

const archiver = require('archiver')
const { generateSafeFilename } = require('./file-download')

/**
 * 设置ZIP下载响应头
 * @param {object} ctx - Koa上下文
 * @param {string} zipName - ZIP文件名
 */
function setupZipResponse(ctx, zipName) {
  const safeZipName = generateSafeFilename(zipName, 0)
  
  // 设置响应头
  ctx.set('Content-Type', 'application/zip')
  ctx.set('Content-Disposition', `attachment; filename="${encodeURIComponent(safeZipName)}"`)
  
  // 设置成功状态码
  ctx.status = 200
}

/**
 * 创建ZIP压缩器
 * @param {object} ctx - Koa上下文
 * @returns {object} archiver实例
 */
function createZipArchiver(ctx) {
  const archive = archiver('zip', {
    zlib: { level: 1 } // 使用快速压缩，图片文件压缩效果有限
  })

  // 将ZIP流直接写入响应
  // 确保Koa正确识别为流：手动pipe到响应
  archive.pipe(ctx.res)
  
  // 设置ctx.respond = false 告诉Koa我们手动处理响应
  ctx.respond = false

  // 处理压缩器错误
  archive.on('error', (err) => {
    console.error('ZIP压缩错误:', err)
    // 注意：一旦设置了流响应，就不能再改变ctx.body
    // 只能记录错误，让流自然结束
  })

  return archive
}

/**
 * 将文件添加到ZIP压缩器
 * @param {object} archive - archiver实例
 * @param {Array} successFiles - 成功下载的文件列表
 */
function addFilesToZip(archive, successFiles) {
  successFiles.forEach(file => {
    archive.append(file.buffer, { name: file.filename })
  })
}

/**
 * 将摘要文件添加到ZIP
 * @param {object} archive - archiver实例
 * @param {object} summary - 摘要对象
 */
function addSummaryToZip(archive, summary) {
  archive.append(JSON.stringify(summary, null, 2), { name: 'download_summary.json' })
}

/**
 * 完成ZIP打包并发送响应
 * @param {object} archive - archiver实例
 * @returns {Promise<void>}
 */
async function finalizeZip(archive) {
  return new Promise((resolve, reject) => {
    archive.on('end', () => {
      console.log('ZIP压缩完成')
      resolve()
    })

    archive.on('error', (err) => {
      console.error('ZIP压缩错误:', err)
      reject(err)
    })

    // 完成压缩
    archive.finalize()
  })
}

/**
 * 创建完整的ZIP响应流程
 * @param {object} ctx - Koa上下文
 * @param {string} zipName - ZIP文件名
 * @param {Array} successFiles - 成功下载的文件列表
 * @param {object} summary - 下载摘要
 * @returns {Promise<void>}
 */
async function createZipResponse(ctx, zipName, successFiles, summary) {
  // 设置响应头
  setupZipResponse(ctx, zipName)
  
  // 创建ZIP压缩器
  const archive = createZipArchiver(ctx)
  
  // 添加文件到ZIP
  addFilesToZip(archive, successFiles)
  
  // 添加摘要文件
  // addSummaryToZip(archive, summary)
  
  // 完成压缩（不等待完成，让流自然处理）
  archive.finalize()
  
  console.log(`ZIP打包开始: ${successFiles.length} 个文件`)
}

module.exports = {
  setupZipResponse,
  createZipArchiver,
  addFilesToZip,
  addSummaryToZip,
  finalizeZip,
  createZipResponse
}
