module.exports = ({ env }) => {
  const bosConfig = {
    prod: {
      AK: '0c7d26b7627d4e97a1a3e679575042bf',
      SK: '25e81d25cdb442c29fe4c74861340a03',
      Bucket: 'ayx-kbs',
      Endpoint: 'https://ayx-kbs.bj.bcebos.com',
      customHost: 'https://jzl-oss.yunxiao.com',
      baseDir: 'jiaoyan/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
    gray: {
      AK: '0c7d26b7627d4e97a1a3e679575042bf',
      SK: '25e81d25cdb442c29fe4c74861340a03',
      Bucket: 'ayx-kbs',
      Endpoint: 'https://ayx-kbs.bj.bcebos.com',
      customHost: 'https://jzl-oss.yunxiao.com',
      baseDir: 'jiaoyan/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
    test: {
      AK: '0c7d26b7627d4e97a1a3e679575042bf',
      SK: '25e81d25cdb442c29fe4c74861340a03',
      Bucket: 'ayx-kbs',
      Endpoint: 'https://ayx-kbs.bj.bcebos.com',
      customHost: 'https://jzl-oss.yunxiao.com',
      baseDir: 'jiaoyan/test/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
    local: {
      AK: '0c7d26b7627d4e97a1a3e679575042bf',
      SK: '25e81d25cdb442c29fe4c74861340a03',
      Bucket: 'ayx-kbs',
      Endpoint: 'https://ayx-kbs.bj.bcebos.com',
      customHost: 'https://jzl-oss.yunxiao.com',
      baseDir: 'jiaoyan/test/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
  }[env('SERVER', 'prod')]

  const userConfig = {
    prod: {
      email: '<EMAIL>',
      password: 'wlyAdmin123!@#'
    },
    gray: {
      email: '<EMAIL>',
      password: 'wlyAdmin123!@#'
    },
    test: {
      email: '<EMAIL>',
      password: '123456'
    },
    local: {
      email: '<EMAIL>',
      password: '123456'
    },
  }[env('SERVER', 'prod')]

  return {
    upload: {
      objectStorage: {
        target: 'bos',
        baseDir: bosConfig.baseDir,
        uploadPath: bosConfig.uploadPath,
        richTextUploadPath: bosConfig.richTextUploadPath,
        customHost: bosConfig.customHost,
        config: {
          // Bos Config
          AK: bosConfig.AK,
          SK: bosConfig.SK,
          Bucket: bosConfig.Bucket,
          Endpoint: bosConfig.Endpoint
        }
      }
    },
    usersPermissions: {
      adminUser: {
        email: userConfig.email,
        password: userConfig.password
      }
    },
  }
}
