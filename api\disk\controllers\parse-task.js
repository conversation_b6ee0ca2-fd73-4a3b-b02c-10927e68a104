'use strict'
const { CurdRouter } = require('accel-utils')
const _ = require('lodash')
const Joi = require('joi')
// 移除不再需要的导入，这些功能已经移到services中
const enums = require('../../common/enums')
const { createAndStartTask, validateTaskAccess, getTaskFiles } = require('../services/parse-task')
const fileDownloadService = require('../services/file-download')
const zipPackageService = require('../services/zip-package')

const curdRouter = new (class extends CurdRouter {
})(enums.CollectionName.PARSE_TASK)

const JOI_CREATE_TASK = Joi.object({
  disk_files: Joi.array().items(Joi.string().required().length(24)),
  task_type: Joi.string().default('word'),
  name: Joi.string(),
  grade: Joi.string().required(),
  parse_type: Joi.string().required(),
  from_year: Joi.number().required(),
  to_year: Joi.number().required(),
})


async function createTask (ctx) {
  const { error, value } = JOI_CREATE_TASK.validate(ctx.request.body)
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)

  const user = ctx.state.user
  const { disk_files, task_type, grade, parse_type, from_year, to_year, name } = value

  // 检查文件是否存在
  const files = await strapi.query(enums.CollectionName.DISK_FILE).find({ id_in: disk_files })
  if (_.isEmpty(files) || files.length !== disk_files.length || files.some(file => file.pBranch.id !== user.pBranch.id))
    return ctx.wrapper.error('HANDLE_ERROR', `文件不存在或异常`)
  if (files.some(file => file.deleted === enums.Bool.YES))
    return ctx.wrapper.error('HANDLE_ERROR', `文件已删除`)

  // 创建并启动解析任务
  const taskResult = await createAndStartTask(user, files, task_type, { grade, parse_type, from_year, to_year, name })

  return ctx.wrapper.succ({ id: taskResult.id })
}

// 这些函数已经移动到 services/file-download.js 中

const JOI_DOWNLOAD_ZIP = Joi.object({
  id: Joi.string().required().length(24),
  zipName: Joi.string().default('images.zip'),
  timeout: Joi.number().default(30000).min(1000).max(300000)
})

/**
 * 批量下载解析任务的图片文件并打包成ZIP
 * @param {Object} ctx - Koa上下文
 */
async function downloadTaskZip(ctx) {
  try {
    const { error, value } = JOI_DOWNLOAD_ZIP.validate(ctx.request.body)
    if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)

    const user = ctx.state.user
    const { id, zipName, timeout } = value

    // 验证任务权限和有效性
    const task = await validateTaskAccess(id, user)

    // 获取任务关联的有效文件列表
    const validFiles = await getTaskFiles(task)

    console.log(`开始批量下载解析任务 ${task.id} 的 ${validFiles.length} 个文件`)

    // 4. 批量下载文件
    const results = await fileDownloadService.batchDownloadFiles(validFiles, timeout)

    // 如果没有成功下载任何文件，返回错误
    if (results.success.length === 0) {
      return ctx.wrapper.error('HANDLE_ERROR', '没有成功下载任何文件')
    }

    // 5. 创建下载摘要
    // const summary = fileDownloadService.createDownloadSummary(task, validFiles, results)

    // 6. 创建ZIP响应并发送
    await zipPackageService.createZipResponse(ctx, zipName, results.success)

    console.log(`批量下载完成: 成功 ${results.success.length}/${validFiles.length} 个文件`)

    // 明确返回，避免404状态码
    return

  } catch (error) {
    console.error('批量下载ZIP错误:', error)

    // 如果是服务层的业务错误，直接返回错误信息
    if (!ctx.headerSent) {
      return ctx.wrapper.error('HANDLE_ERROR', `批量下载失败: ${error.message}`)
    }

    // 如果已经发送了响应头（ZIP流已开始），无法返回JSON错误，只能记录日志
    console.error('ZIP流已开始，无法返回错误响应')
    return
  }
}



module.exports = {
  ...curdRouter.createHandlers(),
  createTask,
  downloadTaskZip,
}
