'use strict'
const { CurdRouter } = require('accel-utils')
const _ = require('lodash')
const Joi = require('joi')
const axios = require('axios')
const archiver = require('archiver')
const path = require('path')
const enums = require('../../common/enums')
const { createAndStartTask } = require('../services/parse-task')

const curdRouter = new (class extends CurdRouter {
})(enums.CollectionName.PARSE_TASK)

const JOI_CREATE_TASK = Joi.object({
  disk_files: Joi.array().items(Joi.string().required().length(24)),
  task_type: Joi.string().default('word'),
  name: Joi.string(),
  grade: Joi.string().required(),
  parse_type: Joi.string().required(),
  from_year: Joi.number().required(),
  to_year: Joi.number().required(),
})


async function createTask (ctx) {
  const { error, value } = JOI_CREATE_TASK.validate(ctx.request.body)
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)

  const user = ctx.state.user
  const { disk_files, task_type, grade, parse_type, from_year, to_year, name } = value

  // 检查文件是否存在
  const files = await strapi.query(enums.CollectionName.DISK_FILE).find({ id_in: disk_files })
  if (_.isEmpty(files) || files.length !== disk_files.length || files.some(file => file.pBranch.id !== user.pBranch.id))
    return ctx.wrapper.error('HANDLE_ERROR', `文件不存在或异常`)
  if (files.some(file => file.deleted === enums.Bool.YES))
    return ctx.wrapper.error('HANDLE_ERROR', `文件已删除`)

  // 创建并启动解析任务
  const taskResult = await createAndStartTask(user, files, task_type, { grade, parse_type, from_year, to_year, name })

  return ctx.wrapper.succ({ id: taskResult.id })
}

/**
 * 从URL下载文件
 * @param {string} url - 文件URL
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<Buffer>} 文件Buffer
 */
async function downloadFileFromUrl(url, timeout = 30000) {
  try {
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      timeout: timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    return Buffer.from(response.data)
  } catch (error) {
    throw new Error(`下载文件失败: ${url} - ${error.message}`)
  }
}

/**
 * 生成安全的文件名
 * @param {string} filename - 原始文件名
 * @param {number} index - 文件索引
 * @returns {string} 安全的文件名
 */
function generateSafeFilename(filename, index) {
  if (!filename) {
    return `image_${index}.jpg`
  }

  // 移除不安全的字符
  const safeName = filename.replace(/[<>:"/\\|?*]/g, '_')

  // 如果文件名太长，截断它
  if (safeName.length > 200) {
    const ext = path.extname(safeName).toLowerCase() || '.jpg'
    const nameWithoutExt = safeName.substring(0, 200 - ext.length)
    return `${nameWithoutExt}${ext}`
  }

  return safeName
}

const JOI_DOWNLOAD_ZIP = Joi.object({
  id: Joi.string().required().length(24),
  zipName: Joi.string().default('images.zip'),
  timeout: Joi.number().default(30000).min(1000).max(300000)
})

/**
 * 批量下载解析任务的图片文件并打包成ZIP
 * @param {Object} ctx - Koa上下文
 */
async function downloadTaskZip(ctx) {
  try {
    const { error, value } = JOI_DOWNLOAD_ZIP.validate(ctx.request.body)
    if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)

    const user = ctx.state.user
    const { id, zipName, timeout } = value

    // 查找解析任务
    const task = await strapi.query(enums.CollectionName.PARSE_TASK).findOne({
      id: id,
      creator: user.id,
      pBranch: user.pBranch.id,
      deleted: enums.Bool.NO
    })

    if (!task) {
      return ctx.wrapper.error('HANDLE_ERROR', '解析任务不存在或无权限访问')
    }

    // 检查任务类型是否为images
    // if (task.task_type !== 'images') {
    //   return ctx.wrapper.error('HANDLE_ERROR', '只有图片解析任务才支持批量下载ZIP')
    // }

    // 获取关联的磁盘文件
    const diskFiles = await strapi.query(enums.CollectionName.DISK_FILE).find({
      id_in: task.disk_files || [],
      deleted: enums.Bool.NO
    })

    if (_.isEmpty(diskFiles)) {
      return ctx.wrapper.error('HANDLE_ERROR', '未找到相关的图片文件')
    }

    // 过滤出有效的URL
    const validFiles = diskFiles.filter(file => file.url && file.url.trim())

    if (validFiles.length === 0) {
      return ctx.wrapper.error('HANDLE_ERROR', '没有有效的图片文件URL')
    }

    if (validFiles.length > 10) {
      return ctx.wrapper.error('HANDLE_ERROR', '文件数量过多，一次最多支持10个文件')
    }

    console.log(`开始批量下载解析任务 ${task.id} 的 ${validFiles.length} 个文件`)

    // 记录成功和失败的文件
    const results = {
      success: [],
      failed: []
    }

    // 先下载所有文件到内存，然后再创建ZIP流
    // 并发下载文件（限制并发数为5）
    const concurrencyLimit = 5
    const downloadPromises = []

    for (let i = 0; i < validFiles.length; i += concurrencyLimit) {
      const batch = validFiles.slice(i, i + concurrencyLimit)
      const batchPromises = batch.map(async (file, batchIndex) => {
        const actualIndex = i + batchIndex

        try {
          console.log(`开始下载文件 ${actualIndex + 1}/${validFiles.length}: ${file.name}`)

          // 下载文件
          const fileBuffer = await downloadFileFromUrl(file.url, timeout)

          // 生成文件名，优先使用原始文件名
          const filename = generateSafeFilename(file.name, actualIndex + 1)

          results.success.push({
            id: file.id,
            name: file.name,
            filename: filename,
            buffer: fileBuffer,
            size: fileBuffer.length,
            url: file.url
          })

          console.log(`文件下载成功: ${filename} (${fileBuffer.length} bytes)`)

        } catch (error) {
          console.error(`文件下载失败 ${actualIndex + 1}:`, error.message)
          results.failed.push({
            id: file.id,
            name: file.name,
            url: file.url,
            error: error.message
          })
        }
      })

      downloadPromises.push(...batchPromises)

      // 等待当前批次完成再处理下一批次
      await Promise.all(batchPromises)
    }

    // 等待所有下载完成
    await Promise.all(downloadPromises)

    // 如果没有成功下载任何文件，返回错误
    if (results.success.length === 0) {
      return ctx.wrapper.error('HANDLE_ERROR', '没有成功下载任何文件')
    }

    // 现在开始创建ZIP流响应
    const archive = archiver('zip', {
      zlib: { level: 1 } // 使用快速压缩，图片文件压缩效果有限
    })

    // 设置响应头
    const safeZipName = generateSafeFilename(zipName, 0)
    ctx.set('Content-Type', 'application/zip')
    ctx.set('Content-Disposition', `attachment; filename="${encodeURIComponent(safeZipName)}"`)

    // 处理压缩器错误
    archive.on('error', (err) => {
      console.error('ZIP压缩错误:', err)
    })

    // 将ZIP流直接写入响应
    ctx.body = archive

    // 添加所有成功下载的文件到ZIP
    results.success.forEach(file => {
      archive.append(file.buffer, { name: file.filename })
    })

    // 添加下载结果摘要文件（移除buffer字段避免JSON过大）
    const summaryFiles = results.success.map(file => ({
      id: file.id,
      name: file.name,
      filename: file.filename,
      size: file.size,
      url: file.url
    }))

    const summary = {
      taskId: task.id,
      taskName: task.name,
      taskType: task.task_type,
      downloadTime: new Date().toISOString(),
      totalFiles: validFiles.length,
      successCount: results.success.length,
      failedCount: results.failed.length,
      successFiles: summaryFiles,
      failedFiles: results.failed
    }

    archive.append(JSON.stringify(summary, null, 2), { name: 'download_summary.json' })

    console.log(`批量下载完成: 成功 ${results.success.length}/${validFiles.length} 个文件`)

    // 完成压缩
    archive.finalize()

  } catch (error) {
    console.error('批量下载ZIP错误:', error)
    if (!ctx.headerSent) {
      return ctx.wrapper.error('HANDLE_ERROR', `批量下载失败: ${error.message}`)
    }
  }
}



module.exports = {
  ...curdRouter.createHandlers(),
  createTask,
  downloadTaskZip,
}
