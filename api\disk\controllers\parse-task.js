'use strict'
const { CurdRouter } = require('accel-utils')
const _ = require('lodash')
const Joi = require('joi')
const enums = require('../../common/enums')
const { createAndStartTask } = require('../services/parse-task')

const curdRouter = new (class extends CurdRouter {
})(enums.CollectionName.PARSE_TASK)

const JOI_CREATE_TASK = Joi.object({
  disk_files: Joi.array().items(Joi.string().required().length(24)),
  task_type: Joi.string().default('word'),
  name: Joi.string(),
  grade: Joi.string().required(),
  parse_type: Joi.string().required(),
  from_year: Joi.number().required(),
  to_year: Joi.number().required(),
})

async function createTask (ctx) {
  const { error, value } = JOI_CREATE_TASK.validate(ctx.request.body)
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)

  const user = ctx.state.user
  const { disk_files, task_type, grade, parse_type, from_year, to_year, name } = value

  // 检查文件是否存在
  const files = await strapi.query(enums.CollectionName.DISK_FILE).find({ id_in: disk_files })
  if (_.isEmpty(files) || files.length !== disk_files.length || files.some(file => file.pBranch.id !== user.pBranch.id))
    return ctx.wrapper.error('HANDLE_ERROR', `文件不存在或异常`)
  if (files.some(file => file.deleted === enums.Bool.YES))
    return ctx.wrapper.error('HANDLE_ERROR', `文件已删除`)

  // 创建并启动解析任务
  const taskResult = await createAndStartTask(user, files, task_type, { grade, parse_type, from_year, to_year, name })

  return ctx.wrapper.succ({ id: taskResult.id })
}



module.exports = {
  ...curdRouter.createHandlers(),
  createTask,
}
