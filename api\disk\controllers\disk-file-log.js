const { CurdRouter } = require('accel-utils');
const _ = require('lodash');
const Joi = require('joi')
const enums = require('../../common/enums');
const curdRouter = new CurdRouter(enums.CollectionName.DISK_FILE_LOG);

module.exports = {
  ...curdRouter.createHandlers(),
  getList,
  deleteById,
}

const JOI_GET_LIST = Joi.object({
  offset: Joi.number().integer().required(),
  limit: Joi.number().integer().required(),
  period: Joi.string().required(),
  subject: Joi.string().required(),
  name: Joi.string().optional().allow(''),
  operation_type: Joi.string().valid(...Object.values(enums.FileOperationType)).required(),
});

async function getList(ctx) {
  const {error, value} = JOI_GET_LIST.validate(ctx.request.query);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const query = {
    period: value.period,
    subject: value.subject,
    operation_type: value.operation_type,
    creator: user.id,
    deleted: enums.Bool.NO,
    _start: value.offset,
    _limit: value.limit,
    _sort: 'createdAt:DESC',
  }
  if (value.name) query['name_contains'] = value.name;
  const result = {
    total: 0,
    list: []
  };
  result.total = await strapi.query(enums.CollectionName.DISK_FILE_LOG).count(query);
  if (!result.total) return ctx.wrapper.succ(result);
  result.list = await strapi.query(enums.CollectionName.DISK_FILE_LOG).find(query);
  for (const data of result.list) {
    data.file = _.pick(data.file, ['id', 'name', 'size', 'url', 'type', 'category', 'parent_id', 'suffix']);
    delete data.deleted;
    delete data.creator;
    // delete data.createdAt;
    // delete data.updatedAt;
  }
  return ctx.wrapper.succ(result);
}

const JOI_DELETE_BY_ID = Joi.object({
  id: Joi.string().required(),
});

async function deleteById(ctx) {
  const { error, value } = JOI_DELETE_BY_ID.validate(ctx.params);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const { id } = value;
  await strapi.query(enums.CollectionName.DISK_FILE_LOG).update({id: id, creator: user.id}, {deleted: enums.Bool.YES});
  return ctx.wrapper.succ({id});
}



