const { CurdRouter } = require('accel-utils');
const _ = require('lodash');
const Joi = require('joi')
const client = require('../../common/client');
const enums = require('../../common/enums');
const studyService = require('../../common/services/study');
const basketService = require('../services/basket');
const questionService = require('../../question/services/question');
const MODEL_BASKET = 'basket';
const curdRouter = new CurdRouter(MODEL_BASKET);

module.exports = {
  ...curdRouter.createHandlers(),
  getQuestions,
  addQuestions,
  deleteQuestions,
  getBasket,
  deleteBasket,
}

/**
 * 获取试题篮
 * @param ctx
 * @returns {Promise<[]>}
 */
async function getQuestions(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.query;
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  const result = await basketService.getUserBasketSimple(user, studyInfo.period, studyInfo.subject);
  return ctx.wrapper.succ(result);
}

const JOI_ADD_QUESTION = Joi.object({
  period: Joi.string().required(),
  subject: Joi.string().required(),
  questions: Joi.array().items(Joi.object({
    source: Joi.string().required(),
    source_id: Joi.string().required(),
    type: Joi.string().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
  })).min(1).required()
});

/**
 * 添加试题
 * @param ctx
 * @returns {Promise<[]|void>}
 */
async function addQuestions(ctx) {
  const {error, value} = JOI_ADD_QUESTION.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const params = _.assign({}, value); // ctx.request.body;
  const { questions } = ctx.request.body;
  if (!_.size(questions)) {
    return ctx.wrapper.error('HANDLE_ERROR', '试题不能为空');
  }
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  // 添加试题
  await basketService.pushQuestions(user, studyInfo.period, studyInfo.subject, questions);

  const result = await basketService.getUserBasketSimple(user, studyInfo.period, studyInfo.subject);
  return ctx.wrapper.succ(result);
}

/**
 * 删除试题
 * @param ctx
 * @returns {Promise<[]|*[]>}
 */
async function deleteQuestions(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.body;
  const { questions } = ctx.request.body;
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  await basketService.deleteQuestions(user, questions);
  const result = await basketService.getUserBasketSimple(user, studyInfo.period, studyInfo.subject);
  return ctx.wrapper.succ(result);
}

async function getBasket(ctx) {
  const user = ctx.state.user;
  const user_id = user.id;
  const params = ctx.request.query;
  const { period, subject, paper_id, op = 'delete', source, template } = params;
  const basket = await basketService.initBasket(user_id);
  basket.period = period;
  basket.subject = subject;
  // basket.parts_list = ["name", "subtitle", "paper_info", "gutter", "attentions", "volumes", "blocks", "secret_tag"];
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  let userQuestions = await basketService.getUserBasketQuestions(user, studyInfo.period, studyInfo.subject);
  if (paper_id) {
    if (op === 'delete') {
      await basketService.deleteBasket(user, studyInfo.period, studyInfo.subject);
      // 删除同学段、科目数据
      userQuestions = [];
    }
    const fun = HANDLER_PAPER[source];
    let { paper, questions }= await fun(paper_id, user_id);
    // 校验试题数量
    const newQuestions = questions.filter( e => !(userQuestions || []).find(uq => uq.source_id === e.id.toString()) );
    // 保存试题
    if (_.size(newQuestions)) {
      newQuestions.forEach(e => {
        if (!e.source) e.source = enums.QuestionSource.SYS;
        if (!e.source_id) e.source_id = e.id.toString();
      })
      await basketService.pushQuestions(user, studyInfo.period, studyInfo.subject, newQuestions);
      userQuestions = await basketService.getUserBasketQuestions(user, studyInfo.period, studyInfo.subject);
    }
    // 处理试题篮复用信息
    if (paper.name) {
      basket.name = paper.name + '(副本)';
    }
    if (paper.template) {
      basket.template = template;
    }
    if (paper.parts_list) {
      basket.parts_list = paper.parts_list;
    }
    if (paper.grade) {
      basket.grade = paper.grade;
    }
    if (paper.type) {
      basket.type = paper.type;
    }
  }
  // 插入试题
  const kb_ids = _.chain(userQuestions).filter(e => e.source === enums.QuestionSource.SYS).map(e => e.source_id).value();;
  let quesMap = {};
  if (_.size(kb_ids)) {
    const questions = await client.kb.getQuestionByIds(kb_ids);
    quesMap = _.keyBy(questions, 'id');
  }
  // 教研试题
  const jy_ids = _.chain(userQuestions).filter(e => e.source === enums.QuestionSource.JY).map(e => e.source_id).value();
  let jyQuesMap = {};
  if (_.size(jy_ids)) {
    const questions = await questionService.getByIds(user, jy_ids);
    jyQuesMap = _.keyBy(questions, 'id');
  }
  for (const q of userQuestions) {
    let question = {};
    if (q.source === enums.QuestionSource.SYS) {
      question =quesMap[q.source_id];
    } else {
      question =jyQuesMap[q.source_id];
    }
    if (_.isEmpty(question)) continue;
    q.period = period;
    q.subject = subject;
    await basketService.insertQuestion(basket, q);
  }
  await basketService.traverseQuestions(basket, quesMap);
  await basketService.renderBasket(basket);
  return ctx.wrapper.succ(basket);
}

async function deleteBasket(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.body;
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  await basketService.deleteBasket(user, studyInfo.period, studyInfo.subject);
  return ctx.wrapper.succ([]);
}


const HANDLER_PAPER = {
  [enums.PaperSourceType.SYS]: _handler_sys_paper,
  [enums.PaperSourceType.ASSEMBLE]: _handler_assemble_paper,
  // [enums.PaperSourceType.UPLOAD]: _handler_upload_exampaper,
}


async function _handler_sys_paper(paper_id) {
  // 系统试卷库
  const paper = await client.kb.getPaperById(paper_id);
  const questions = [];
  for (const b of _.get(paper, 'blocks', [])) {
    for (const ques of b.questions) {
      questions.push(ques);
    }
  }
  return { paper, questions };
}


async function _handler_assemble_paper(id) {
  const paper = await strapi.query('exampaper').findOne({id: id});
  if (_.isEmpty(paper) || paper.status !== enums.PaperStatus.DONE) {
    throw new Error('组卷不存在');
  }
  const questions = [];
  for (const volume of _.get(paper, 'volumes', [])) {
    for (const block of _.get(volume, 'blocks', [])) {
      for (const question of _.get(block, 'questions', [])) {
        questions.push(question);
      }
    }
  }
  return { paper, questions };
}
