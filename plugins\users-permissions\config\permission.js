// app
const { createDefaultPermissions: CP } = require('accel-utils')
const apps = []
// 基础视图配置
const pageGroups = [
  {
    sId: 'PlatformConfig',
    name: '超级管理🦸‍♂️',
    isSystem: true,
    pages: [
      { sId: 'BranchConfig', name: '租户管理', icon: 'business'},
      { sId: 'AppConfig', name: '应用配置', icon: 'apps'},
      { sId: 'UserConfig', name: '用户管理', icon: 'account_box'},
      {
        sId: 'UserVariable', name: '用户变量', icon: 'data_object',
        meta: {
          modelId: 'plugins::users-permissions.user-variable',
          modelPath: 'users-permissions/user-variables',
        }
      },
      { sId: 'RoleConfig', name: '角色配置', icon: 'supervised_user_circle' },
      { sId: 'GroupConfig', name: '功能配置', icon: 'app_settings_alt' },
      { sId: 'PageConfig', name: '页面权限', icon: 'preview' },
      { sId: 'ApiConfig', name: '接口权限', icon: 'api' },
      { sId: 'MenuConfig', name: '菜单配置', icon: 'menu' },
      {
        sId: 'FilterConfig', name: '筛选器管理', icon: 'filter',
        meta: {
          modelId: 'plugins::users-permissions.filter',
          modelPath: 'users-permissions/filters',
        }
      },
      { sId: 'SystemInfo', name: '系统信息', icon: 'display_settings' },
      {
        sId: 'TokenConfig', name: '临时令牌', icon: 'token',
        meta: {
          modelId: 'plugins::users-permissions.token',
          modelPath: 'users-permissions/tokens',
        }
      },
    ],
  }
]
// 基础功能配置
const functions = [
  // 基础模块功能
  {
    name: '超级管理',
    sId: 'SuperAdminFunction',
    pages: [],
    apiPermissions: [
      // 超级管理员快速登录任意用户
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'superQuickLogin' },
      { 'type': 'users-permissions', 'controller': 'content-types', 'action': 'manualRefresh' }
    ]
  },
  {
    name: '已登录',
    sId: 'AuthenticatedFunction',
    apiPermissions: [
      // 用户信息 | C端用户注册登录
      { 'type': 'users-permissions', 'controller': 'user', 'action': 'me' },
      { 'type': 'users-permissions', 'controller': 'user', 'action': 'updateMe' },
      { 'type': 'users-permissions', 'controller': 'user', 'action': 'changeCurrentRole' },
      { 'type': 'users-permissions', 'controller': 'user', 'action': 'changeCurrentBranch' },
      { 'type': 'users-permissions', 'controller': 'user', 'action': 'getVariable' },
      { 'type': 'users-permissions', 'controller': 'user', 'action': 'setVariable' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'checkToken' },
      // 获取数据模型
      { 'type': 'users-permissions', 'controller': 'content-types', 'action': 'findOneModel' },
      { 'type': 'users-permissions', 'controller': 'content-types', 'action': 'findRelations' },
      { 'type': 'users-permissions', 'controller': 'content-types', 'action': 'getRowRelationRows' },

      // 页面管理
      ...CP({ type: 'users-permissions', controller: 'page', role: 'read' }),
      // 新租户创建 | 加入租户
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'createNewBranch' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'joinBranchByToken' },
      // ---------------------------------- 同步未登录权限 ----------------------------------
      // 账号注册 | 账号密码登录 | 手机验证码登录
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'register' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'loginByAccount' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'loginByYxWeCom' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'sendLoginCode' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'loginByPhoneAndCode' },
      // 忘记密码 | 获取 App Token | 解析 App Token [useless] | TokenKey 登录
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'forgotPasswordByEmail' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'getUserByResetPasswordCode' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'resetPasswordByCode' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'getAppAuthToken' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'checkToken' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'loginByTokenKey' },
      { 'type': 'users-permissions', 'controller': 'token', 'action': 'findOne' },
      // 微信小程序登录 | 微信小程序手机号绑定 | 微信公众号扫码登录
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'loginByMiniprogram' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'loginByOfficialAccount' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'bindMiniprogramPhoneNumber' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getMiniprogramUrlLink' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getMiniprogramQRCode' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getMiniprogramACode' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getOffiaccountQRCodeTicket' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'checkOffiaccountLogin' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getUserOffiaccountInfo' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getOffiaccountSignature' },
      // 微信公众号扫码登录 - 协同回调
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'verifyOffiaccountEvent' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'handleOffiaccountEvent' },
      // 域名租户信息接口
      { 'type': 'users-permissions', 'controller': 'branch', 'action': 'fetchDomainBranchInfo' },
      { 'type': 'users-permissions', 'controller': 'app', 'action': 'find' },
      { 'type': 'users-permissions', 'controller': 'collection', 'action': 'create' },
      { 'type': 'users-permissions', 'controller': 'filter', 'action': 'addFilter' },
      { 'type': 'users-permissions', 'controller': 'filter', 'action': 'changeFilter' },
      { 'type': 'users-permissions', 'controller': 'filter', 'action': 'getFilters' },
      { 'type': 'users-permissions', 'controller': 'filter', 'action': 'deleteFilter' },

      // ---------------------------------- 同步未登录权限 ----------------------------------
    ]
  },
  {
    name: '未登录',
    sId: 'PublicFunction',
    apiPermissions: [
      // 账号注册 | 账号密码登录 | 手机验证码登录
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'register' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'loginByAccount' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'loginByYxWeCom' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'sendLoginCode' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'loginByPhoneAndCode' },
      // 忘记密码 | 获取 App Token | 解析 App Token [useless] | TokenKey 登录
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'forgotPasswordByEmail' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'getUserByResetPasswordCode' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'resetPasswordByCode' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'getAppAuthToken' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'checkToken' },
      { 'type': 'users-permissions', 'controller': 'auth-extend', 'action': 'loginByTokenKey' },
      // 微信小程序登录 | 微信小程序手机号绑定 | 微信公众号扫码登录
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'loginByMiniprogram' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'loginByOfficialAccount' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'bindMiniprogramPhoneNumber' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getMiniprogramUrlLink' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getMiniprogramQRCode' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getMiniprogramACode' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getOffiaccountQRCodeTicket' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'checkOffiaccountLogin' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'getOffiaccountSignature' },
      // 微信公众号扫码登录 - 协同回调
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'verifyOffiaccountEvent' },
      { 'type': 'users-permissions', 'controller': 'auth-wechat', 'action': 'handleOffiaccountEvent' },
      // 基础数据模型获取接口
      { 'type': 'users-permissions', 'controller': 'content-types', 'action': 'findOneModel' },
      // 域名租户信息接口
      { 'type': 'users-permissions', 'controller': 'branch', 'action': 'fetchDomainBranchInfo' },
      { 'type': 'users-permissions', 'controller': 'app', 'action': 'find' },
    ]
  },
  {
    name: '平台配置',
    sId: 'CoreAdminFunction',
    pages: [
      'BranchConfig',
      'MenuConfig',
      'RoleConfig',
      'GroupConfig',
      'PageConfig',
      'ApiConfig',
      'AppConfig',
      'SystemInfo',
      'TokenConfig',
      'FilterConfig'
    ],
    apiPermissions: [
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'getRole' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'getRoles' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'createRole' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'deleteRole' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'updateRole' },
      { 'type': 'users-permissions', 'controller': 'app', 'action': 'find' },
      { 'type': 'users-permissions', 'controller': 'app', 'action': 'count' },
      { 'type': 'users-permissions', 'controller': 'app', 'action': 'create' },
      { 'type': 'users-permissions', 'controller': 'app', 'action': 'update' },
      { 'type': 'users-permissions', 'controller': 'app', 'action': 'delete' },
      { 'type': 'users-permissions', 'controller': 'menu', 'action': 'setBaseMenu' },
      { 'type': 'users-permissions', 'controller': 'menu', 'action': 'getBaseMenu' },
      ...CP({ type: 'users-permissions', controller: 'page' }),
      ...CP({ type: 'users-permissions', controller: 'group' }),
      ...CP({ type: 'users-permissions', controller: 'branch' }),
      ...CP({ type: 'users-permissions', controller: 'token' }),
      ...CP({ type: 'users-permissions', controller: 'filter' }),

      { 'type': 'users-permissions', 'controller': 'branch', 'action': 'fetchDomainBranchInfo' },
      { 'type': 'users-permissions', 'controller': 'branch', 'action': 'fetchCurrentBranch' },
    ]
  },
  {
    name: '用户管理',
    sId: 'UserManagementFunction',
    pages: [
      'UserConfig',
      'UserVariable',
    ],
    apiPermissions: [
      ...CP({ type: 'users-permissions', controller: 'user' }),
      ...CP({ type: 'users-permissions', controller: 'user-variable' }),
    ]
  },
  {
    name: '租户用户管理',
    sId: 'BranchUserManagementFunction',
    pages: [],
    apiPermissions: [
      // 租户下用户管理
      ...CP({
        type: 'users-permissions',
        controller: 'user',
        mode: 'branch'
      }),
      { 'type': 'users-permissions', 'controller': 'branch', 'action': 'update' },
    ]
  },
  {
    name: '高级数据管理',
    sId: 'CoreContentPermissionFunction',
    pages: [],
    // 允许查看与更新内容配置
    apiPermissions: [
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'getRoutes' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'index' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'searchUsers' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'updateRole' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'getPermissions' },
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'getPolicies' },
      { 'type': 'users-permissions', 'controller': 'collection', 'action': 'find' },
      { 'type': 'users-permissions', 'controller': 'collection', 'action': 'count' },
      { 'type': 'users-permissions', 'controller': 'collection', 'action': 'findOne' },
      { 'type': 'users-permissions', 'controller': 'collection', 'action': 'create' },
      { 'type': 'users-permissions', 'controller': 'collection', 'action': 'update' },
      { 'type': 'users-permissions', 'controller': 'collection', 'action': 'delete' },
      // 内容管理
      { 'type': 'users-permissions', 'controller': 'userspermissions', 'action': 'getRoles' },
      { 'type': 'users-permissions', 'controller': 'content-types', 'action': 'findOneModel' },
      { 'type': 'users-permissions', 'controller': 'content-types', 'action': 'findContentTypes' },
      { 'type': 'users-permissions', 'controller': 'relations', 'action': 'find' },
    ]
  }
]

// 基础角色
const roles = [
  {
    name: '未登录',
    type: 'public',
    description: '公共功能，未登录状态下也可以使用，例如注册、登录功能',
    modules: [
      'PublicFunction'
    ]
  },
  {
    name: '普通用户',
    type: 'authenticated',
    description: '系统默认的普通用户角色，拥有基础功能权限',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction'
    ]
  }
]

// 基础租户
const branches = [
  { name: '默认租户', type: 'default' },
]
// 查询过滤器
const queryFilters = [
  // Example
  // {
  //   type: 'users-permissions',
  //   controller: 'relations',
  //   action: 'find',
  //   callback (ctx) {
  //     const user = ctx.state.user
  //     if (user.role && user.role.type !== 'SuperAdmin') {
  //       const { model, targetField } = ctx.params
  //       if (model === 'plugins::users-permissions.user' && targetField === 'role') {
  //         ctx.request.query = {
  //           type_in: ['branch-admin', 'branch-user'],
  //           ...ctx.request.query,
  //         }
  //       }
  //     }
  //   }
  // }
]

const settings = {
  local_setting_first: false,
  unique_email: true,
  allow_register: true,
  email_confirmation: false,
  email_reset_password: null,
  email_confirmation_redirection: null,
  default_role: 'authenticated',
}

module.exports = {
  usersPermissionsConfig: {
    pageGroups,
    functions,
    roles,
    apps,
    branches,
    queryFilters,
    settings
  }
}
