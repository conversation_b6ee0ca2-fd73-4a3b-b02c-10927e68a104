const _ = require('lodash');
const enums = require('../enums')

module.exports = {
  pickFields,
  getStandardSubject,
}

/**
 * 提取字段
 * @param data {object} 数据
 * @param fields {[]}
 * @returns {{}|Pick<*, keyof *>}
 */
function pickFields(data, fields = ['id', 'name']) {
  if (!_.size(fields)) return {};
  return _.pick(data, fields);
}

function getStandardSubject(period, subject) {
  let result = null;
  for (let key in enums.SubjectRules) {
    if (key === subject) {
      result = subject;
      break;
    }
    const rules = enums.SubjectRules[key];
    if (rules.includes(subject)) {
      result = key;
      break;
    }
  }
  if (period === '小学' && subject === '政治') result = '道德与法治';
  return result;
}
