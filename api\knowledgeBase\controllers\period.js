'use strict'
const { BranchCurdRouter } = require('accel-utils')
const { ObjectId } = require('mongodb')
const axios = require('axios')
const _ = require('lodash')
const branchCurdRouter = new (class extends BranchCurdRouter {
  async branchUpdate (ctx) {
    const { branchId, data } = this._parseBranchCtx(ctx)
    const userId = ctx.state.user.id
    if (data.name) {
      data.key = `${data.name}-${branchId}`
    }
    data.operator = userId
    return super.branchUpdate(ctx)
  }
})('period')

// 异步获取学校教材目录
// 根据当前用户学校ID，查询与该学校关联的学期、科目、教材版本和教材信息
async function getBookCatalogByBranch (ctx) {
  // 获取当前用户所在学校的ID
  const branchId = ctx.state.user.pBranch?.id
  // 查询与学校ID关联的所有学期，并按学期名称升序排序，同时获取每个学期的科目信息
  const periods = await strapi.query('period').find({
    pBranch: branchId,
    _sort: 'name:ASC',
    _limit: -1,
  }, ['subjects',])
  const subjectIds = _.flatten(periods.map(period => period.subjects.map(subject => subject.id)))
  // 删除学校下 异常科目
  await strapi.query('subject').model.deleteMany({
    _id: { $nin: subjectIds },
    pBranch: branchId,
  })

  // 从所有学期的科目中提取教材ID，形成一个扁平化的教材ID数组
  const pressVersionIds = _.flatten(_.flatten(periods.map(period => period.subjects.map(subject => subject.press_versions))))
  // 查询教材版本,同时获取每个教材版本的教材分册信息
  const pressVersions = await strapi.query('press-version').find({
    id_in: pressVersionIds,
    // subject_in: subjectIds,
    // _sort: 'name:ASC',
    _limit: -1,
  }, ['books'])
  // 删除学校下 异常教材版本
  await strapi.query('press-version').model.deleteMany({
    _id: { $nin: pressVersionIds },
    pBranch: branchId,
  })
  const bookIds = _.flatten(pressVersions.map(pressVersion => pressVersion.books.map(book => book.id)))
  // 删除学校下 异常教材
  await strapi.query('book').model.deleteMany({
    _id: { $nin: bookIds },
    pBranch: branchId,
  })

  // 初始化返回对象，用于组织教材目录信息
  let returnObj = {
    book: {
      children: [],
    }
  }
  // 遍历每个学期，构建学期对象，并将其添加到返回对象中
  for (const period of periods) {
    let periodObj = {
      key: 'period',
      name: period.name,
      id: period.id,
      source: period.source,
      source_id: period.source_id,
      children: [],
    }
    // 遍历学期中的每个科目，构建科目对象，并将其添加到当前学期对象中
    for (const subject of period.subjects) {
      let subjectObj = {
        key: 'subject',
        name: subject.name,
        id: subject.id,
        source: subject.source,
        source_id: subject.source_id,
        children: [],
      }
      // 遍历与当前科目关联的教材版本，构建教材版本对象，并将其添加到当前科目对象中
      for (const pressVersion of pressVersions.filter(e => e.subject.toString() === subject.id)) {
        let pressVersionObj = {
          key: 'press_version',
          name: pressVersion.name,
          id: pressVersion.id,
          source: pressVersion.source,
          source_id: pressVersion.source_id,
          children: [],
        }
        // 遍历教材版本中的每本教材，构建教材对象，并将其添加到当前教材版本对象中
        for (const book of pressVersion.books) {
          let bookObj = {
            key: 'press_volume',
            name: book.name,
            id: book.id,
            source: book.source,
            source_id: book.source_id,
          }
          pressVersionObj.children.push(bookObj)
        }
        subjectObj.children.push(pressVersionObj)
      }
      periodObj.children.push(subjectObj)
    }
    returnObj.book.children.push(periodObj)
  }

  // 返回组织好的教材目录信息
  return ctx.wrapper.succ(returnObj)
}

// 异步获取学校学段学科目录
async function getPeriodCatalogByBranch (ctx) {
  // 获取当前用户所在学校的ID
  const branchId = ctx.state.user.pBranch?.id
  // 查询与学校ID关联的所有学期，并按学期名称升序排序，同时获取每个学期的科目信息
  const periods = await strapi.query('period').find({
    pBranch: branchId,
    _sort: 'name:ASC',
    _limit: -1,
  }, ['subjects',])
  const subjectIds = _.flatten(periods.map(period => period.subjects.map(subject => subject.id)))
  // 删除学校下 异常科目
  await strapi.query('subject').model.deleteMany({
    _id: { $nin: subjectIds },
    pBranch: branchId,
  })

  // 初始化返回对象，用于组织教材目录信息
  let returnObj = {
    book: {
      children: [],
    }
  }
  // 遍历每个学期，构建学期对象，并将其添加到返回对象中
  for (const period of periods) {
    let periodObj = {
      key: 'period',
      name: period.name,
      id: period.id,
      source: period.source,
      source_id: period.source_id,
      children: [],
    }
    // 遍历学期中的每个科目，构建科目对象，并将其添加到当前学期对象中
    for (const subject of period.subjects) {
      let subjectObj = {
        key: 'subject',
        name: subject.name,
        id: subject.id,
        source: subject.source,
        source_id: subject.source_id,
      }
      periodObj.children.push(subjectObj)
    }
    returnObj.book.children.push(periodObj)
  }

  // 返回组织好的教材目录信息
  return ctx.wrapper.succ(returnObj)
}

// 同步新增本校学段学科教材
async function syncBookCatalogByBranch (ctx) {
  const { book } = ctx.request.body
  const branchId = ctx.state.user.pBranch?.id
  const userId = ctx.state.user.id
  if (!branchId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户异常')
  }
  // 验证输入数据
  if (!book || !Array.isArray(book.children)) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '无效的输入数据')
  }
  try {
    let periodInsertArray = [], subjectInsertArray = [], versionInsertArray = [], bookInsertArray = [], bookChapterInsertArray = []
    let periodBulkWriteArray = [], subjectBulkWriteArray = [], versionBulkWriteArray = []
    // 遍历整个目录 并创建新节点和更新关联次序
    for (let period of book.children) {
      if (!period.id) {
        // const newPeriod = await strapi.query('period').create({
        const newPeriod = {
          _id: new ObjectId().toString(),
          name: period.name,
          source: period.source,
          key: `${period.name}-${branchId}`,
          operator: userId,
          creator: userId,
          pBranch: branchId,
        }
        periodInsertArray.push(newPeriod)
        period.id = newPeriod._id
      }

      let subjects = []
      for (let subject of period?.children || []) {
        if (!subject.id) {
          const newSubject = {
            _id: new ObjectId().toString(),
            name: subject.name,
            source: subject.source,
            period: period.id,
            key: `${subject.name}-${period.id}`,
            operator: userId,
            creator: userId,
            pBranch: branchId,
          }
          subjectInsertArray.push(newSubject)
          subject.id = newSubject._id
        }
        subjects.push(subject.id)
        let pressVersions = []
        for (let version of subject?.children || []) {
          if (!version.id) {
            const newPressVersion = {
              _id: new ObjectId().toString(),
              name: version.name,
              source: version.source,
              subject: subject.id,
              period: period.id,
              key: `${version.name}-${subject.id}`,
              operator: userId,
              creator: userId,
              pBranch: branchId,
            }
            versionInsertArray.push(newPressVersion)
            version.id = newPressVersion._id
          }
          pressVersions.push(version.id)
          let books = []
          for (let book of version?.children || []) {
            if (!book.id) {
              const bookId = new ObjectId().toString()
              const bookChapterId = new ObjectId().toString()
              // 创建book和顶层章节
              const newBook = {
                _id: bookId,
                name: book.name,
                source: book.source,
                source_id: book.source_id,
                press_version: version.id,
                period: period.id,
                subject: subject.id,
                key: `${book.name}-${version.id}`,
                operator: userId,
                creator: userId,
                pBranch: branchId,
                book_chapter: bookChapterId,
                children: [],
              }
              bookInsertArray.push(newBook)
              book.id = newBook._id

              const newChapter = {
                id: bookChapterId,
                book: book.id,
                name: book.name,
                path: `${period.id}-${subject.id}-${version.id}-${bookChapterId}`,
                // key: `${period.id}-${subject.id}-${version.id}-${book.name}`,
                period: period.id,
                subject: subject.id,
                source: book.source,
                source_id: book.source_id,
                press_version: version.id,
                operator: userId,
                creator: userId,
                pBranch: branchId,
              }
              bookChapterInsertArray.push(newChapter)
            }
            books.push(book.id)
          }
          // 更新教材版本，关联教材分册和次序
          if (books.length > 0) {
            versionBulkWriteArray.push({
              updateOne: {
                filter: { _id: version.id },
                update: { $set: { books: books } },
              },
            })
          }
          // await strapi.query('press-version').update({ id: version.id, }, { books: books, })
        }
        // 更新学科，关联教材版本和次序
        if (pressVersions.length > 0) {
          subjectBulkWriteArray.push({
            updateOne: {
              filter: { _id: subject.id },
              update: { $set: { press_versions: pressVersions } },
            },
          })
        }
        // await strapi.query('subject').update({ id: subject.id, }, { press_versions: pressVersions, })
      }
      // 更新学段，关联学科和次序
      if (subjects.length > 0) {
        periodBulkWriteArray.push({
          updateOne: {
            filter: { _id: period.id },
            update: { $set: { subjects: subjects } },
          },
        })
      }
      // await strapi.query('period').update({ id: period.id, }, { subjects: subjects, })
    }
    // 批量插入或更新
    if (periodInsertArray.length > 0) {
      let result = await strapi.query('period').model.insertMany(periodInsertArray)
      // console.log(result)
    }
    if (subjectInsertArray.length > 0) {
      let result = await strapi.query('subject').model.insertMany(subjectInsertArray)
      // console.log(result)
    }
    if (versionInsertArray.length > 0) {
      let result = await strapi.query('press-version').model.insertMany(versionInsertArray)
      // console.log(result)
    }
    if (bookInsertArray.length > 0) {
      let result = await strapi.query('book').model.insertMany(bookInsertArray)
      // console.log(result)
    }
    if (bookChapterInsertArray.length > 0) {
      let result = await strapi.query('book-chapter').model.insertMany(bookChapterInsertArray)
      // console.log(result)
    }
    if (periodBulkWriteArray.length > 0) {
      let result = await strapi.query('period').model.bulkWrite(periodBulkWriteArray)
      // console.log(result)
    }
    if (subjectBulkWriteArray.length > 0) {
      let result = await strapi.query('subject').model.bulkWrite(subjectBulkWriteArray)
      // console.log(result)
    }
    if (versionBulkWriteArray.length > 0) {
      let result = await strapi.query('press-version').model.bulkWrite(versionBulkWriteArray)
      // console.log(result)
    }

    return ctx.wrapper.succ({ book })
  } catch (e) {
    if (e.message === 'Duplicate entry') {
      console.error(e) // 记录错误日志
      return ctx.wrapper.error('HANDLE_ERROR', '数据重复', { book })
    }
    console.error('Error syncing book catalog:', e) // 记录错误日志
    throw e
  }
}


module.exports = {
  getBookCatalogByBranch,
  syncBookCatalogByBranch,
  getPeriodCatalogByBranch,
  ...branchCurdRouter.createHandlers(),
}