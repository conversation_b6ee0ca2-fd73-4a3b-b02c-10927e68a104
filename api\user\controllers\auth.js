const client = require('../../common/client')
const { processUnifyToken } = require('../services/auth')

async function loginByUnifyToken (ctx) {
  const userPlugin = strapi.plugins['users-permissions']
  const unifyToken = ctx.request.body.unifyToken

  if (!unifyToken) {
    return ctx.unauthorized('Token 无效，请重新登录')
  }
  const result = await processUnifyToken(unifyToken)

  if (!result.success) {
    return ctx.unauthorized(result.error)
  }

  const user = result.user

  const returnObj = userPlugin.services['user'].getFullAuthData(user)
  returnObj.user = user
  if (user.yjUserId) user.authRoles = await client.yj.getTeacherAuthRoles(user.yjUserId)
  return ctx.wrapper.succ(returnObj)
}

module.exports = {
  loginByUnifyToken
}
