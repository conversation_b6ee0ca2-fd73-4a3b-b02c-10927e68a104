const koaBody = require('koa-body')
const path = require('path')

module.exports = strapi => {
  return {
    initialize () {
      strapi.app.use(/** @type {*} */koaBody({
        jsonLimit: '10mb', // JSON 请求体大小限制
        formLimit: '10mb', // 表单请求体大小限制
        textLimit: '10mb', // 文本请求体大小限制
        multipart: true,
        formidable: {
          uploadDir: path.resolve(__dirname, '../../public/uploads'),
          keepExtensions: true,
          maxFieldsSize: 10 * 1024 * 1024, // 10mb
          maxFileSize: 10 * 1024 * 1024 * 1024,
        },
      }))
    },
  }
}
