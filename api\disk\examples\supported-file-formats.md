# 支持的文件格式

解析任务文件批量下载ZIP功能支持多种文件格式，包括但不限于：

## 📷 图片格式
- **JPEG/JPG** - 常见的照片格式
- **PNG** - 支持透明背景的图片格式
- **GIF** - 动画图片格式
- **WebP** - 现代高效图片格式
- **BMP** - 位图格式
- **TIFF** - 高质量图片格式
- **SVG** - 矢量图形格式

## 📄 文档格式
- **PDF** - 便携式文档格式
- **DOC/DOCX** - Microsoft Word文档
- **XLS/XLSX** - Microsoft Excel电子表格
- **PPT/PPTX** - Microsoft PowerPoint演示文稿
- **TXT** - 纯文本文件
- **RTF** - 富文本格式
- **ODT** - OpenDocument文本文档
- **ODS** - OpenDocument电子表格
- **ODP** - OpenDocument演示文稿

## 🗜️ 压缩格式
- **ZIP** - 标准压缩格式
- **RAR** - WinRAR压缩格式
- **7Z** - 7-Zip压缩格式
- **TAR** - Unix归档格式
- **GZ** - Gzip压缩格式

## 🎵 音频格式
- **MP3** - 常见音频格式
- **WAV** - 无损音频格式
- **AAC** - 高级音频编码
- **FLAC** - 无损音频压缩
- **OGG** - 开源音频格式

## 🎬 视频格式
- **MP4** - 常见视频格式
- **AVI** - 音视频交错格式
- **MOV** - QuickTime视频格式
- **WMV** - Windows媒体视频
- **FLV** - Flash视频格式
- **MKV** - Matroska视频格式

## 💻 代码文件
- **HTML** - 网页标记语言
- **CSS** - 样式表文件
- **JS** - JavaScript脚本
- **JSON** - 数据交换格式
- **XML** - 可扩展标记语言
- **CSV** - 逗号分隔值文件
- **SQL** - 数据库查询语言
- **MD** - Markdown文档

## 🔧 其他格式
- **EXE** - 可执行文件
- **DLL** - 动态链接库
- **ISO** - 光盘镜像文件
- **APK** - Android应用包
- **IPA** - iOS应用包

## 📊 使用统计

根据实际使用情况，最常见的文件格式包括：

1. **图片类** (60%): JPG, PNG, GIF, WebP
2. **文档类** (25%): PDF, DOCX, XLSX, PPTX
3. **压缩类** (10%): ZIP, RAR, 7Z
4. **其他类** (5%): TXT, CSV, JSON, XML

## ⚠️ 注意事项

1. **文件大小限制**: 单个文件最大支持100MB
2. **文件数量限制**: 单次下载最多100个文件
3. **总大小限制**: 单次下载总大小不超过1GB
4. **超时设置**: 默认30秒超时，可通过参数调整
5. **并发限制**: 最多5个文件并发下载，避免服务器压力

## 🚀 性能优化

- **流式处理**: 大文件采用流式下载，减少内存占用
- **并发控制**: 智能并发控制，平衡速度和资源消耗
- **压缩优化**: 根据文件类型选择最佳压缩策略
- **缓存机制**: 支持文件缓存，提高重复下载效率

## 📝 示例用法

```javascript
// 下载包含多种格式文件的任务
const response = await fetch('/parse-tasks/actions/downloadZip', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    id: 'task-id',
    zipName: 'mixed-files.zip',
    timeout: 30000
  })
})

// 响应将包含所有格式的文件打包成ZIP
const blob = await response.blob()
```

## 🔍 文件类型检测

系统会自动检测文件类型，无需手动指定：

- 通过文件扩展名识别
- 通过MIME类型验证
- 通过文件头信息确认
- 支持无扩展名文件的智能识别

这确保了所有文件都能被正确处理和打包。
