/**
 * 批量下载文件并打包成ZIP的使用示例
 * 
 * 这个文件展示了如何使用批量下载ZIP功能
 */

const axios = require('axios')

// 示例1: 基本用法 - 使用字符串URL数组
async function basicExample() {
  const urls = [
    'https://example.com/file1.pdf',
    'https://example.com/file2.jpg',
    'https://example.com/file3.docx'
  ]
  
  try {
    const response = await axios.post('http://localhost:1337/upload/external/batch-download-zip', {
      urls: urls,
      zipName: 'my-files.zip',
      timeout: 30000 // 30秒超时
    }, {
      responseType: 'stream'
    })
    
    // 保存ZIP文件
    const fs = require('fs')
    const writer = fs.createWriteStream('./downloaded-files.zip')
    response.data.pipe(writer)
    
    writer.on('finish', () => {
      console.log('ZIP文件下载完成: downloaded-files.zip')
    })
    
    writer.on('error', (err) => {
      console.error('保存文件失败:', err)
    })
    
  } catch (error) {
    console.error('批量下载失败:', error.message)
  }
}

// 示例2: 高级用法 - 使用对象格式指定自定义文件名
async function advancedExample() {
  const urls = [
    {
      url: 'https://example.com/document.pdf',
      filename: '重要文档.pdf'
    },
    {
      url: 'https://example.com/image.jpg',
      filename: '图片1.jpg'
    },
    {
      url: 'https://example.com/data.json'
      // 不指定filename，将自动从URL提取
    },
    'https://example.com/simple-url.txt' // 字符串格式
  ]
  
  try {
    const response = await axios.post('http://localhost:1337/upload/external/batch-download-zip', {
      urls: urls,
      zipName: '自定义文件名.zip',
      timeout: 60000 // 60秒超时
    }, {
      responseType: 'stream'
    })
    
    // 保存ZIP文件
    const fs = require('fs')
    const writer = fs.createWriteStream('./custom-files.zip')
    response.data.pipe(writer)
    
    writer.on('finish', () => {
      console.log('ZIP文件下载完成: custom-files.zip')
    })
    
  } catch (error) {
    console.error('批量下载失败:', error.message)
  }
}

// 示例3: 在浏览器中使用 (前端JavaScript)
function browserExample() {
  const urls = [
    'https://example.com/file1.pdf',
    'https://example.com/file2.jpg'
  ]
  
  fetch('/upload/external/batch-download-zip', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      urls: urls,
      zipName: 'browser-download.zip'
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('下载失败')
    }
    return response.blob()
  })
  .then(blob => {
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'browser-download.zip'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  })
  .catch(error => {
    console.error('下载失败:', error)
  })
}

// 示例4: 错误处理
async function errorHandlingExample() {
  const urls = [
    'https://valid-url.com/file.pdf',
    'https://invalid-url.com/nonexistent.jpg', // 这个会失败
    'https://another-valid-url.com/document.docx'
  ]
  
  try {
    const response = await axios.post('http://localhost:1337/upload/external/batch-download-zip', {
      urls: urls,
      zipName: 'mixed-results.zip'
    }, {
      responseType: 'stream'
    })
    
    // 即使有些文件下载失败，ZIP中仍会包含成功下载的文件
    // 以及一个download_summary.json文件，详细说明哪些文件成功/失败
    
    const fs = require('fs')
    const writer = fs.createWriteStream('./mixed-results.zip')
    response.data.pipe(writer)
    
    writer.on('finish', () => {
      console.log('ZIP文件下载完成，请查看download_summary.json了解详细结果')
    })
    
  } catch (error) {
    console.error('批量下载失败:', error.message)
  }
}

// API参数说明
const apiDocumentation = {
  endpoint: 'POST /upload/external/batch-download-zip',
  requestBody: {
    urls: {
      type: 'Array',
      required: true,
      description: '要下载的URL数组，支持字符串或对象格式',
      examples: [
        ['url1', 'url2', 'url3'], // 字符串数组
        [
          { url: 'url1', filename: 'custom1.pdf' },
          { url: 'url2', filename: 'custom2.jpg' },
          'url3' // 混合格式
        ]
      ]
    },
    zipName: {
      type: 'String',
      required: false,
      default: 'files.zip',
      description: 'ZIP文件名'
    },
    timeout: {
      type: 'Number',
      required: false,
      default: 30000,
      description: '单个文件下载超时时间（毫秒）'
    }
  },
  response: {
    success: 'ZIP文件流',
    error: {
      code: 'HANDLE_ERROR',
      message: '错误描述'
    }
  },
  limitations: {
    maxFiles: 100,
    concurrency: 5,
    timeout: '30秒/文件'
  },
  zipContents: [
    '成功下载的文件',
    'download_summary.json (包含下载结果详情)'
  ]
}

module.exports = {
  basicExample,
  advancedExample,
  browserExample,
  errorHandlingExample,
  apiDocumentation
}

// 如果直接运行此文件，执行基本示例
if (require.main === module) {
  console.log('运行批量下载示例...')
  basicExample()
}
