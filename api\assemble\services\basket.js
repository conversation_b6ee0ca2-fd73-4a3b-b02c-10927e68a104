const _ = require('lodash')
const client = require('../../common/client');
const enums = require('../../common/enums');
const { TYPES, DIGIT_MAP_CHINESE } = require('../../common/enums/template');
const studyService = require('../../common/services/study');

const MODEL_BASKET = 'basket';

module.exports = {
  getUserBasketQuestions,
  pushQuestions,
  getUserBasketSimple,
  deleteQuestions,
  deleteBasket,
  initBasket,
  insertQuestion,
  traverseQuestions,
  renderBasket,
}

async function getUserBasketQuestions(user, period, subject) {
  const query = {user: user.id, period, subject};
  const list = await strapi.query(MODEL_BASKET).find(query);
  if (_.size(list)) {
    list.forEach(e => {
      e.period = e.period.name;
      e.subject = e.subject.name;
    });
  }
  return list;
}

async function getUserBasketSimple(user, period, subject) {
  const user_id = user.id;
  const questions = await getUserBasketQuestions(user, period, subject);
  let result = [];
  if (!_.size(questions)) return result;
  const basket = _init_basket(user_id);
  for (const ques of questions) {
    _insert_questions(basket, ques);
  }
  result = _profile_basket(basket);
  return result;
}

/**
 *
 * @param user
 * @param period
 * @param subject
 * @param questions
 * @returns {Promise<void>}
 */
async function pushQuestions(user, period, subject, questions) {
  const userQuestions = await getUserBasketQuestions(user, period, subject);
  const newQuestions = questions.filter(e => {
    if (!_.size(userQuestions)) return true;
    return !userQuestions.find(uq => uq.source === e.source && uq.source_id === e.source_id);
  });
  if (!_.size(newQuestions)) throw new Error('试题不可重复添加');
  const total = _.size(userQuestions) + _.size(newQuestions);
  if (total > enums.BasketQuestionLimit) {
    throw new Error('试题篮数量超过上限');
  }
  // 保存试题
  for (const q of newQuestions) {
    const data = _.assign({}, q);
    data.period = period;
    data.subject = subject;
    data.user = user.id;
    userQuestions.push(data);
    const res = await strapi.query('basket').create(data);
    data.id = res.id;
  }
  return userQuestions;
}

async function deleteQuestions(user, questions = []) {
  for (const q of questions) {
      await strapi.query('basket').delete({user: user.id, source: q.source, source_id: q.source_id});
  }
}

async function deleteBasket(user, period, subject) {
  await strapi.query('basket').delete({user: user.id, period, subject});
}

async function initBasket(user_id) {
  return _init_basket(user_id);
}

function _init_basket(user_id) {
  let basket = {
    user_id: user_id,     // 用户唯一标记, app + user_id
    // 基本信息
    period: '',       // 学段
    subject: '',      // 学科
    name: null,         // 试卷名称
    subtitle: null,     // 副标题
    score: null,        // 试卷总分
    duration: null,     // 考试时间，单位分钟
    paper_info: null,   // 试卷信息栏，
    cand_info: null,
    attentions: null,   // 注意事项
    secret_tag: null,   // 保密标记文字
    gutter: null,       // 装订线
    template: 'homework',   // 组卷类型，stardard, exam, homework
    parts_list: ["name", "cand_info"],
    // basket.parts_list = ["name", "subtitle", "paper_info", "gutter", "attentions", "volumes", "blocks", "secret_tag"];
    // 试卷结构
    volumes: [{
      title: '卷I（选择题）',  // 卷I分卷名
      //note: '请点击修改第I卷的文字说明', // 分卷说明
      note: '', // 分卷说明
      blocks: [],
    }, {
      title: '卷II（非选择题）', // 卷II分卷名
      //note: '请点击修改第II卷的文字说明', // 分卷说明
      note: '', // 分卷说明
      blocks: [],
    }]
  }
  return basket;
}


async function insertQuestion(basket, question) {
  _insert_questions(basket, question);
}

async function traverseQuestions(basket, quesMap) {
  const fields = ['id', 'elite', 'subject', 'period', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type', 'score', 'refer_exampapers', 'year', 'ctime', 'utime'];
  for (const volume of basket.volumes || []) {
    for (const block of volume.blocks || []) {
      for (const i in block.questions) {
        const question = block.questions[i];
        const q = quesMap[question.id];
        if (!q) {
          delete block.questions[i];
          continue;
        }
        // 去除返回的试题电子化答案、解析、解答等信息
        // if (q.blocks) {
        //   q.blocks = {
        //     stems: q.blocks.stems,
        //     knowledges: q.blocks.knowledges,
        //     types: q.blocks.types,
        //   }
        // }
        block.questions[i] = _.pick(q, fields);
        block.questions[i]['score'] = question['score'];
        block.questions[i]['source'] = question['source'];
        block.questions[i]['source_id'] = question['source_id'];
      }
    }
  }
}
/**
 * 插入试题篮
 * @param basket
 * @param ques
 * @returns {*}
 * @private
 */
function _insert_questions(basket, ques) {
  const type = ques['type'];
  const type_t = TYPES[type] || TYPES['default'];

  const vol_pos = type_t['vol_pos'];
  const volume = basket.volumes[vol_pos];

  basket.period = ques['period'] || '';
  basket.subject = ques['subject'] || '';
  if (ques['exampaper_type'] && ques['exampaper_type'] !== enums.ExamPaperOtherType) {
    basket.type = ques['exampaper_type'];
  }

  const ques_ = {
    id: ques['source_id'] || ques['id'],
    period: ques['period'],
    subject: ques['subject'],
    source: ques.source || 'sys',
    source_id: ques.source_id || ques['id']
  }

  for (const b in volume.blocks) {
    const block = volume.blocks[b];
    if (block.type !== type) {
      continue
    }
    // deduplicated
    const questions = block.questions;
    for (const q in questions) {
      if (questions[q]['id'] === (ques['source_id'] || ques['id'])) {
        return basket;
      }
    }
    ques_['score'] = ques['score'] ? ques['score'] : block['default_score'];
    block.questions.push(ques_);
    return basket;
  }
  // init a new block and push the question
  ques_['score'] = ques['score'] ? ques['score'] : type_t['default_score'];
  const block = {
    title: '',
    note: '',
    type: type,
    default_score: type_t['default_score'],
    questions: [ques_],
  };
  // find the proper postion to insert the block
  const blk_pos = type_t['blk_pos'];
  for (const i in volume.blocks) {
    const type_t_ = TYPES[volume.blocks[i].type] || TYPES['default'];
    const pos = type_t_['blk_pos'];
    if (pos > blk_pos) {
      volume.blocks.splice(parseInt(i), 0, block);
      return basket;
    }
  }
  // not find proper postion, then insert to the last
  volume.blocks.push(block);
  return basket;
}

/**
 * 获取试题篮摘要信息
 * @param basket
 * @returns {[]}
 * @private
 */
function _profile_basket(basket) {
  let profile = [];
  if (!basket) {
    return profile;
  }
  let volumes = basket.volumes;
  if (!volumes) {
    return profile;
  }
  for (let volume of volumes) {
    let blocks = volume['blocks'] || [];
    for (let block of blocks) {
      if (!block) {
        continue;
      }
      let type = block['type'];
      let questions = block['questions'];
      profile.push({
        type: type,
        questions: questions.map(function (x) {
          return {
            id: x['id'],
            period: x['period'],
            subject: x['subject'],
            source: x['source'],
            source_id: x['source_id'],
          };
        }),
      });
    }
  }
  return profile;
}

async function renderBasket(basket) {
  // 试题篮分卷信息
  basket.score = 0;
  let block_num = 0;
  const volumes = basket.volumes;
  for (const v in volumes) {
    const volume = volumes[v];
    const blocks = volume.blocks;
    for (const b in blocks) {
      const block = blocks[b];
      const questions = block.questions;
      const n = questions.length;
      if (questions.length <= 0) {
        continue;
      }
      let s = Number(questions[0].score);
      let ts = Number(s);
      let tag = true;
      for (let i = 1; i < n; ++i) {
        if (questions[i].score !== questions[i - 1].score) {
          tag = false;
        }
        ts += Number(questions[i].score);
      }
      const order = `${DIGIT_MAP_CHINESE[++block_num]}`;
      const ss = tag ? `，每题${s}分` : '';
      block.default_score = tag ? s : block.default_score;
      const detail = `本大题共计${n}小题${ss}，共计${ts}分`;
      const note = (block.note.length > 0) ? `，${block.note}` : '';
      block.title = `${order}、${block.type}（${detail}${note}）`;
      // block.title = `${block.type}（${detail}${note}）`;
      basket.score += Number(ts);
    }
  }
  // 试卷名称， 2017年5月3日
  // var t = new Date();
  // var info = `${t.getFullYear()}年${t.getMonth() + 1}月${t.getDate()}日${basket.period}${basket.subject}`;
  basket.name = basket.name || genExamPaperName(basket);
  // 副标题
  basket.subtitle = basket.subtitle || '';
  // 考试时间，单位分钟
  basket.duration = basket.duration || 120;
  // 试卷信息栏，考试总分：100分；考试时间：100分钟
  const paper_info = `考试总分：${basket.score}   考试时间：${basket.duration}`;
  basket.paper_info = basket.paper_info || paper_info;
  // 候选人信息栏
  const line = '__________ ';
  const cand_info = `学校：${line}班级：${line}姓名：${line}考号：${line}`;

  basket.cand_info = basket.cand_info || cand_info;
  // 注意事项
  basket.attentions = basket.attentions || '注意事项：<br>1．答题前填写好自己的姓名、班级、考号等信息; <br>2．请将答案正确填写在答题卡上;<br>';
  // 保密标记文字
  basket.secret_tag = basket.secret_tag || '绝密★启用前';
  // 装订线
  basket.gutter = basket.gutter || 0;
  return basket;
}

function genExamPaperName(basket) {
  const nowDate = new Date();
  const year = nowDate.getFullYear();

  // 学年
  const academicYearStartDate = new Date();
  academicYearStartDate.setMonth(0, 1);
  academicYearStartDate.setHours(0, 0, 0, 0);

  const academicYearEndDate = new Date();
  academicYearEndDate.setMonth(7, 15);
  academicYearEndDate.setHours(0, 0, 0, 0);
  let academicYearLong = `${year.toString()}-${(year + 1).toString()}`;
  if (nowDate.getTime() >= academicYearStartDate.getTime() && nowDate.getTime() < academicYearEndDate.getTime()) {
    academicYearLong = `${(year - 1).toString()}-${year.toString()}`;
  }
  // 学期
  const semesterStartDate = new Date();
  semesterStartDate.setMonth(1, 15);
  semesterStartDate.setHours(0, 0, 0, 0);

  const semesterEndDate = new Date();
  semesterEndDate.setMonth(7, 15);
  semesterEndDate.setHours(0, 0, 0, 0);

  let semester = "上";//学期
  if (nowDate.getTime() >= semesterStartDate.getTime() && nowDate.getTime() < semesterEndDate.getTime()) {
    semester = '下';
  }
  const formatDateStr = `${nowDate.getFullYear()}年${nowDate.getMonth()+1}月${nowDate.getDate()}日`;
  let info = `${academicYearLong}学年${basket.period}${basket.subject} (${semester}) ${basket.type || ''}试卷(${formatDateStr})`;
  return info;
}


const HANDLER_PAPER = {
  [enums.PaperSourceType.SYS]: _handler_sys_paper,
  [enums.PaperSourceType.ASSEMBLE]: _handler_assemble_paper,
  // [enums.PaperSourceType.UPLOAD]: _handler_upload_exampaper,
}


async function _handler_sys_paper(paper_id) {
  // 系统试卷库
  const paper = await client.kb.getPaperById(paper_id);
  const questions = [];
  for (const b of _.get(paper, 'blocks', [])) {
    for (const ques of b.questions) {
      questions.push(ques);
    }
  }
  return { paper, questions };
}


async function _handler_assemble_paper(id) {
  const paper = await strapi.query('exampaper').findOne({id: id});
  if (_.isEmpty(paper)) {
    throw new Error('组卷不存在');
  }
  const questions = [];
  for (const volume of _.get(paper, 'volumes', [])) {
    for (const block of _.get(volume, 'blocks', [])) {
      for (const question of _.get(block, 'questions', [])) {
        questions.push(question);
      }
    }
  }
  return { paper, questions };
}


