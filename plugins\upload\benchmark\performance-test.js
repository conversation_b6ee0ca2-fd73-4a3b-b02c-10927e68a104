/**
 * 批量下载ZIP功能性能测试
 * 测试不同数据量级下的CPU、内存、带宽消耗
 */

const os = require('os')
const process = require('process')
const archiver = require('archiver')
const fs = require('fs')
const path = require('path')

class PerformanceMonitor {
  constructor() {
    this.startTime = 0
    this.startCPU = null
    this.startMemory = 0
    this.peakMemory = 0
  }

  start() {
    this.startTime = Date.now()
    this.startCPU = process.cpuUsage()
    this.startMemory = process.memoryUsage().heapUsed
    this.peakMemory = this.startMemory
    
    // 监控内存峰值
    this.memoryInterval = setInterval(() => {
      const currentMemory = process.memoryUsage().heapUsed
      if (currentMemory > this.peakMemory) {
        this.peakMemory = currentMemory
      }
    }, 100)
  }

  stop() {
    clearInterval(this.memoryInterval)
    
    const endTime = Date.now()
    const endCPU = process.cpuUsage(this.startCPU)
    const endMemory = process.memoryUsage().heapUsed
    
    return {
      duration: endTime - this.startTime,
      cpuUsage: {
        user: endCPU.user / 1000, // 转换为毫秒
        system: endCPU.system / 1000
      },
      memory: {
        start: this.formatBytes(this.startMemory),
        end: this.formatBytes(endMemory),
        peak: this.formatBytes(this.peakMemory),
        increase: this.formatBytes(this.peakMemory - this.startMemory)
      }
    }
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// 生成测试文件
function generateTestFile(size, filename) {
  const buffer = Buffer.alloc(size)
  // 填充随机数据以模拟真实文件
  for (let i = 0; i < size; i++) {
    buffer[i] = Math.floor(Math.random() * 256)
  }
  fs.writeFileSync(filename, buffer)
  return filename
}

// 模拟ZIP压缩测试
async function testZipCompression(files, compressionLevel = 1) {
  const monitor = new PerformanceMonitor()
  monitor.start()
  
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', {
      zlib: { level: compressionLevel }
    })
    
    const outputPath = `./test-output-${Date.now()}.zip`
    const output = fs.createWriteStream(outputPath)
    
    archive.pipe(output)
    
    let totalSize = 0
    
    // 添加文件到压缩包
    files.forEach((file, index) => {
      const stats = fs.statSync(file.path)
      totalSize += stats.size
      archive.file(file.path, { name: file.name })
    })
    
    archive.finalize()
    
    output.on('close', () => {
      const stats = monitor.stop()
      const outputStats = fs.statSync(outputPath)
      
      // 清理测试文件
      fs.unlinkSync(outputPath)
      
      resolve({
        ...stats,
        originalSize: totalSize,
        compressedSize: outputStats.size,
        compressionRatio: (totalSize / outputStats.size).toFixed(2),
        compressionLevel: compressionLevel
      })
    })
    
    archive.on('error', reject)
  })
}

// 性能测试套件
async function runPerformanceTests() {
  console.log('🚀 开始批量下载ZIP性能测试')
  console.log(`系统信息: ${os.cpus().length}核 CPU, ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB 内存`)
  console.log('=' * 60)
  
  const testCases = [
    {
      name: '小文件测试 (10个 × 1MB)',
      fileCount: 10,
      fileSize: 1024 * 1024, // 1MB
    },
    {
      name: '中等文件测试 (20个 × 5MB)', 
      fileCount: 20,
      fileSize: 5 * 1024 * 1024, // 5MB
    },
    {
      name: '大文件测试 (5个 × 20MB)',
      fileCount: 5,
      fileSize: 20 * 1024 * 1024, // 20MB
    },
    {
      name: '超大文件测试 (2个 × 100MB)',
      fileCount: 2,
      fileSize: 100 * 1024 * 1024, // 100MB
    }
  ]
  
  const compressionLevels = [1, 6, 9] // 快速、默认、最高压缩
  
  for (const testCase of testCases) {
    console.log(`\n📊 ${testCase.name}`)
    console.log('-'.repeat(50))
    
    // 生成测试文件
    const testFiles = []
    const tempDir = `./temp-${Date.now()}`
    fs.mkdirSync(tempDir, { recursive: true })
    
    try {
      for (let i = 0; i < testCase.fileCount; i++) {
        const filename = path.join(tempDir, `test-file-${i}.dat`)
        generateTestFile(testCase.fileSize, filename)
        testFiles.push({
          path: filename,
          name: `test-file-${i}.dat`
        })
      }
      
      // 测试不同压缩级别
      for (const level of compressionLevels) {
        console.log(`\n  压缩级别 ${level}:`)
        
        const result = await testZipCompression(testFiles, level)
        
        console.log(`    处理时间: ${result.duration}ms`)
        console.log(`    CPU使用: ${result.cpuUsage.user.toFixed(2)}ms (用户) + ${result.cpuUsage.system.toFixed(2)}ms (系统)`)
        console.log(`    内存使用: ${result.memory.start} → ${result.memory.peak} (峰值增加: ${result.memory.increase})`)
        console.log(`    原始大小: ${(result.originalSize / 1024 / 1024).toFixed(2)}MB`)
        console.log(`    压缩大小: ${(result.compressedSize / 1024 / 1024).toFixed(2)}MB`)
        console.log(`    压缩比: ${result.compressionRatio}:1`)
        console.log(`    处理速度: ${(result.originalSize / 1024 / 1024 / (result.duration / 1000)).toFixed(2)}MB/s`)
        
        // 计算CPU核心占用
        const totalCPUTime = result.cpuUsage.user + result.cpuUsage.system
        const cpuCores = totalCPUTime / result.duration
        console.log(`    CPU核心占用: ${cpuCores.toFixed(3)}核`)
      }
      
    } finally {
      // 清理测试文件
      testFiles.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path)
        }
      })
      if (fs.existsSync(tempDir)) {
        fs.rmdirSync(tempDir)
      }
    }
  }
  
  console.log('\n🎉 性能测试完成!')
  
  // 输出性能建议
  console.log('\n💡 性能建议:')
  console.log('1. 对于小文件(<10MB总量): 可支持20-50并发用户')
  console.log('2. 对于中等文件(50-200MB): 可支持5-15并发用户') 
  console.log('3. 对于大文件(>500MB): 建议限制为1-3并发用户')
  console.log('4. 推荐使用压缩级别1(快速)以平衡性能和压缩比')
  console.log('5. 建议实现队列机制避免服务器过载')
}

// 并发测试
async function testConcurrency(concurrentUsers = 5) {
  console.log(`\n🔄 并发测试 (${concurrentUsers}个用户)`)
  console.log('-'.repeat(50))
  
  const monitor = new PerformanceMonitor()
  monitor.start()
  
  const promises = []
  
  for (let i = 0; i < concurrentUsers; i++) {
    const promise = (async () => {
      const tempDir = `./temp-concurrent-${i}-${Date.now()}`
      fs.mkdirSync(tempDir, { recursive: true })
      
      try {
        // 每个用户下载10个5MB文件
        const files = []
        for (let j = 0; j < 10; j++) {
          const filename = path.join(tempDir, `user-${i}-file-${j}.dat`)
          generateTestFile(5 * 1024 * 1024, filename)
          files.push({ path: filename, name: `file-${j}.dat` })
        }
        
        return await testZipCompression(files, 1)
      } finally {
        // 清理
        if (fs.existsSync(tempDir)) {
          fs.readdirSync(tempDir).forEach(file => {
            fs.unlinkSync(path.join(tempDir, file))
          })
          fs.rmdirSync(tempDir)
        }
      }
    })()
    
    promises.push(promise)
  }
  
  const results = await Promise.all(promises)
  const overallStats = monitor.stop()
  
  console.log(`总处理时间: ${overallStats.duration}ms`)
  console.log(`总CPU使用: ${(overallStats.cpuUsage.user + overallStats.cpuUsage.system).toFixed(2)}ms`)
  console.log(`峰值内存: ${overallStats.memory.peak}`)
  console.log(`平均每用户处理时间: ${results.reduce((sum, r) => sum + r.duration, 0) / results.length}ms`)
  
  const totalCPUTime = overallStats.cpuUsage.user + overallStats.cpuUsage.system
  const cpuCores = totalCPUTime / overallStats.duration
  console.log(`总CPU核心占用: ${cpuCores.toFixed(3)}核`)
}

// 主函数
async function main() {
  try {
    await runPerformanceTests()
    await testConcurrency(3)
    await testConcurrency(5)
  } catch (error) {
    console.error('测试失败:', error)
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main()
}

module.exports = {
  PerformanceMonitor,
  testZipCompression,
  runPerformanceTests,
  testConcurrency
}
