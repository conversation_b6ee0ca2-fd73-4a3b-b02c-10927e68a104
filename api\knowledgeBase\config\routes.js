const { createDefaultRoutes } = require('accel-utils')
module.exports = {
  'routes': [
    ...createDefaultRoutes({
      controller: 'tree-catalog',
      basePath: '/tree-catalogs',
      mode: 'branch'
    }),
    // ...createDefaultRoutes({
    //   controller: 'period',
    //   basePath: '/periods',
    //   mode: 'branch'
    // }),
    // ...createDefaultRoutes({
    //   controller: 'subject',
    //   basePath: '/subjects',
    //   mode: 'branch'
    // }),
    // ...createDefaultRoutes({
    //   controller: 'press-version',
    //   basePath: '/press-versions',
    //   mode: 'branch'
    // }),
    // ...createDefaultRoutes({
    //   controller: 'book',
    //   basePath: '/books',
    //   mode: 'branch'
    // }),
    // ...createDefaultRoutes({
    //   controller: 'book-chapter',
    //   basePath: '/book-chapters',
    //   mode: 'branch'
    // }),
    ...createDefaultRoutes({
      controller: 'knowledge-tree',
      basePath: '/knowledge-trees',
      mode: 'branch'
    }),
    ...createDefaultRoutes({
      controller: 'knowledge-tree-chapter',
      basePath: '/knowledge-tree-chapters',
      mode: 'branch'
    }),
    ...createDefaultRoutes({
      controller: 'knowledge',
      basePath: '/knowledges',
      mode: 'branch'
    }),
    {
      path: `/tree-catalogs/actions/getPeriodCatalog`,
      method: 'GET',
      handler: `tree-catalog.getPeriodCatalog`,
      config: {
        policies: [],
      }
    },
    {
      path: `/tree-catalogs/actions/getBookTreeCatalog`,
      method: 'GET',
      handler: `tree-catalog.getBookTreeCatalog`,
      config: {
        policies: [],
      }
    },
    {
      path: `/tree-catalogs/actions/updatePeriodCatalog`,
      method: 'POST',
      handler: `tree-catalog.updatePeriodCatalog`,
      config: {
        policies: [],
      }
    },
    {
      path: `/tree-catalogs/actions/updateBookTreeCatalog`,
      method: 'POST',
      handler: `tree-catalog.updateBookTreeCatalog`,
      config: {
        policies: [],
      }
    },
    {
      path: `/periods/actions/getBookCatalogByBranch`,
      method: 'GET',
      handler: `period.getBookCatalogByBranch`,
      config: {
        policies: [],
      }
    },
    {
      path: `/periods/actions/syncBookCatalogByBranch`,
      method: 'POST',
      handler: `period.syncBookCatalogByBranch`,
      config: {
        policies: [],
      }
    },
    {
      path: `/periods/actions/getPeriodCatalogByBranch`,
      method: 'GET',
      handler: `period.getPeriodCatalogByBranch`,
      config: {
        policies: [],
      }
    },
    {
      path: `/press-versions/actions/getPressVersionCatalogByBranch`,
      method: 'GET',
      handler: `press-version.getPressVersionCatalogByBranch`,
      config: {
        policies: [],
      }
    },
    {
      path: `/press-versions/actions/syncPressVersionCatalogByBranch`,
      method: 'POST',
      handler: `press-version.syncPressVersionCatalogByBranch`,
      config: {
        policies: [],
      }
    },
    {
      path: `/books/actions/getBookById`,
      method: 'GET',
      handler: `book.getBookById`,
      config: {
        policies: [],
      }
    },
    {
      path: `/books/actions/syncBookById`,
      method: 'POST',
      handler: `book.syncBookById`,
      config: {
        policies: [],
      }
    },
    {
      path: `/knowledge-trees/actions/getKnowledgeTreeByPeriodSubject`,
      method: 'GET',
      handler: `knowledge-tree.getKnowledgeTreeByPeriodSubject`,
      config: {
        policies: [],
      }
    },
    {
      path: `/knowledge-trees/actions/syncKnowledgeTreeById`,
      method: 'POST',
      handler: `knowledge-tree.syncKnowledgeTreeById`,
      config: {
        policies: [],
      }
    },
    {
      path: `/knowledges/actions/getKnowledgeListBySearch`,
      method: 'GET',
      handler: `knowledge.getKnowledgeListBySearch`,
      config: {
        policies: [],
      }
    },
    {
      path: `/knowledges/actions/addKnowledgeByChapter`,
      method: 'POST',
      handler: `knowledge.addKnowledgeByChapter`,
      config: {
        policies: [],
      }
    },
  ]
}
