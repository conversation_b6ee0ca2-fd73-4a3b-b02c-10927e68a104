
const crypto = require('crypto');
const _ = require('lodash');
const URL = require('url');
const axios = require('axios');
const server = strapi.config.server.scantron;

const qs = require('querystring');

module.exports = {
    getGateWay,
};

/**
 * 获取答题卡链接
 * @returns {Promise<*>}
 * @param user
 */
async function getGateWay(userId, userName, schoolId, schoolName, host = '') {
    const object = {
        host: server.url
    };
    // if (host && host.includes('.yxzhixue.com')) object.hostname = 'dtk.yxzhixue.com';
    const url = URL.format(object);
    const query = {
        id: userId,
        name: userName,
        schoolId: schoolId,
        schoolName: schoolName,
        timestamp: Date.now()
    };
    const token = `${query.id}${query.timestamp}${server.sk}`;
    const md5 = crypto.createHash('md5');
    const dtk = md5.update(token).digest('hex');
    query.dtk = dtk;
    const result = {
        url: url,
        query: qs.stringify(query),
        input: query
    }
    return result;
}
