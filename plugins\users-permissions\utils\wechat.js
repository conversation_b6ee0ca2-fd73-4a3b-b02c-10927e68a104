const WXBizDataCrypt = require('./wechat/WXBizDataCrypt')
const axios = require('axios')
const crypto = require('crypto')
const LRU = require('lru-cache')

// 配置缓存选项
const cacheOptions = {
  max: 500, // 最大缓存数量
  maxAge: 1000 * 60 * 60 * 2, // 缓存时间2小时
  updateAgeOnGet: true // 访问时更新过期时间
}

// 替换全局对象为LRU缓存
const accessTokens = new LRU(cacheOptions)
const jsTickets = new LRU(cacheOptions)

// Token 失效可能的状态码
const errCodes = [40001, 40014, 41001, 42001, 48001]

// JSTicket 缓存
const accessTokenServer = strapi.config.plugins?.usersPermissions?.accessTokenServer

// 定期清理过期缓存
setInterval(() => {
  accessTokens.prune()
  jsTickets.prune()
}, 1000 * 60 * 30) // 每30分钟清理一次

// 获取接口调用凭证
// Document https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/access-token/auth.getAccessToken.html
async function getAccessToken({ appid, secret }, forceRefresh = false) {
  // 是否从外部服务获取AccessToken
  if (accessTokenServer) {
    const res = await axios.get(`${accessTokenServer.url}/utils/getAppAccessToken`, {
      params: {
        accessKey: accessTokenServer.accessTokenccessKey,
        appid: appid,
        forceRefresh: forceRefresh,
      }
    })
    const data = res.data
    if (!data.access_token) {
      throw new Error('getAccessToken Error: ' + JSON.stringify(data))
    }
    return data.access_token
  }

  // GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
  const cacheToken = accessTokens.get(appid)
  if (!forceRefresh && cacheToken && new Date() < cacheToken.expiredAt) {
    return accessTokens.get(appid).token
  }
  const res = await axios.get('https://api.weixin.qq.com/cgi-bin/token', {
    params: {
      grant_type: 'client_credential',
      appid: appid,
      secret: secret
    }
  })
  const data = res.data
  if (!data.access_token) {
    throw new Error('getAccessToken Error: ' + JSON.stringify(data))
  }
  accessTokens.set(appid, {
    token: data.access_token,
    expiredAt: new Date(+new Date() + 3600 * 1000)
  })
  return accessTokens.get(appid).token
}

async function getOffiaccountJSTicket({ appid, secret }, forceRefresh = false) {
  const cacheJSTicket = jsTickets.get(appid)
  if (!forceRefresh && cacheJSTicket && new Date() < cacheJSTicket.expiredAt) {
    return jsTickets.get(appid).ticket
  }
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.get(`https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`)
  const data = res.data
  if (errCodes.indexOf(data.errcode) !== -1) {
    await getAccessToken({ appid, secret }, true)
    return await getOffiaccountJSTicket({ appid, secret })
  }

  if (!data.ticket) {
    throw new Error('getJSTicket Error: ' + JSON.stringify(data))
  }
  jsTickets.set(appid, {
    ticket: data.ticket,
    expiredAt: new Date(+new Date() + 3600 * 1000)
  })
  return jsTickets.get(appid).ticket;
}

// 登录 - 获取微信认证数据
// 主要数据： openid & avatar
// Document https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html#%E8%AF%B7%E6%B1%82%E5%9C%B0%E5%9D%80
async function getWechatAuthData({ appid, secret, code }) {

  // Example
  // {
  //   'session_key': 'l50XMduXYQld7ptgyZ2dFQ==',
  //   'openid': 'oUWaE52lHv-CFT-VGcaHWC3Gb1hs',
  // }

  // GET https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code
  const res = await axios.get('https://api.weixin.qq.com/sns/jscode2session',
    {
      params: {
        appid: appid,
        secret: secret,
        js_code: code,
        grant_type: 'authorization_code',
      },
    },
  )
  const wechatServerAuthData = res.data
  const sessionKey = wechatServerAuthData['session_key']

  // OpenID Example 'oUWaE52lHv-CFT-VGcaHWC3Gb1hs'
  const openid = wechatServerAuthData['openid']
  const unionid = wechatServerAuthData['unionid']

  return {
    openid,
    unionid,
  }
}

async function getOfficialAuthData({ appid, secret, code }) {
  const res = await axios.get('https://api.weixin.qq.com/sns/oauth2/access_token',
    {
      params: {
        appid: appid,
        secret: secret,
        code: code,
        grant_type: 'authorization_code',
      },
    },
  )
  const wechatServerAuthData = res.data

  // OpenID Example 'oUWaE52lHv-CFT-VGcaHWC3Gb1hs'
  const openid = wechatServerAuthData['openid']
  const unionid = wechatServerAuthData['unionid']

  return {
    openid,
    unionid,
  }
}

// 获取微信小程序手机号
// Document https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/phonenumber/phonenumber.getPhoneNumber.html
async function getPhoneNumber({ appid, secret, code }) {
  // POST https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=accessToken
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.post(
    `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${accessToken}`,
    {
      code: code
    },
  )
  const data = res.data
  if (errCodes.indexOf(data.errcode) !== -1) {
    await getAccessToken({ appid, secret }, true)
    return await getPhoneNumber({ appid, secret, code })
  }
  return data['phone_info']
}

// 获取公众号二维码 Ticket
// https://developers.weixin.qq.com/doc/offiaccount/Account_Management/Generating_a_Parametric_QR_Code.html
async function genQRCodeTicket({ appid, secret, scene, timeout = 60 }) {
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.post(
    `https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=${accessToken}`,
    {
      'expire_seconds': timeout || 60,
      'action_name': 'QR_STR_SCENE',
      'action_info': {
        'scene': scene,
        'appid': appid,
      }
    }
  )
  const data = res.data
  if (errCodes.indexOf(data.errcode) !== -1) {
    await getAccessToken({ appid, secret }, true)
    return await genQRCodeTicket({ appid, secret, scene, timeout })
  }
  return data
}

// 获取微信公众号用户信息 包含unionid
// https://developers.weixin.qq.com/doc/offiaccount/User_Management/Get_users_basic_information_UnionID.html#UinonId
async function getOffiaccountUserInfo({ appid, secret, openid }) {
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.get(` https://api.weixin.qq.com/cgi-bin/user/info?access_token=${accessToken}&openid=${openid}&lang=zh_CN`)
  const data = res.data
  if (errCodes.indexOf(data.errcode) !== -1) {
    await getAccessToken({ appid, secret }, true)
    return await getOffiaccountUserInfo({ appid, secret, openid })
  }
  // 响应数据实例
  // {
  //   "subscribe": 1,
  //   "openid": "o6_bmjrPTlm6_2sgVt7hMZOPfL2M",
  //   "language": "zh_CN",
  //   "subscribe_time": **********,
  //   "unionid": " o6_bmasdasdsad6_2sgVt7hMZOPfL",
  //   "remark": "",
  //   "groupid": 0,
  //   "tagid_list":[128,2],
  //   "subscribe_scene": "ADD_SCENE_QR_CODE",
  //   "qr_scene": 98765,
  //   "qr_scene_str": ""
  // }
  return data
}

// 获取小程序URL Link
// https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/url-link/generateUrlLink.html
async function genUrlLink({ appid, secret, path, query, env_version }) {
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.post(
    `https://api.weixin.qq.com/wxa/generate_urllink?access_token=${accessToken}`,
    {
      path,
      query,
      env_version
    }
  )
  const data = res.data
  if (errCodes.indexOf(data.errcode) !== -1) {
    await getAccessToken({ appid, secret }, true)
    return await genUrlLink({ appid, secret, path, query, env_version })
  }
  return data
}

// 获取小程序二维码
// https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/createQRCode.html
async function genMiniprogramQRCode({ appid, secret, path, encoding = 'binary' }) {
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.post(
    `https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=${accessToken}`,
    {
      path
    },
    {
      responseEncoding: encoding,
    }
  )
  const data = res.data
  if (errCodes.indexOf(data.errcode) !== -1) {
    await getAccessToken({ appid, secret }, true)
    return await genMiniprogramQRCode({ appid, secret, path })
  }
  return data
}

// 获取小程序码
// https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getQRCode.html
async function genMiniprogramACode({ appid, secret, path, encoding = 'binary' }) {
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.post(
    `https://api.weixin.qq.com/wxa/getwxacode?access_token=${accessToken}`,
    {
      path
    },
    {
      responseEncoding: encoding,
    }
  )
  const data = res.data
  if (errCodes.indexOf(data.errcode) !== -1) {
    await getAccessToken({ appid, secret }, true)
    return await genMiniprogramACode({ appid, secret, path })
  }
  return data
}

/**
 * @descripttion: 扫码登录回调中的 token 解析方法
 * @param {string} secret 解析 token 的私钥
 * @param {string} token 待解析的的 token
 * @param {number} len 解析出来的 token arr 的长度
 * @return {array} [name, departmentId, position, wxId]
 */
function parseJumpToken(secret, token, len) {
  try {
    const decipher = crypto.createDecipher('aes192', secret);
    let decrypted = decipher.update(token, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    const arr = decrypted.split('::');

    const now = Math.floor(Date.now() / 1000);
    if (arr.length === len && arr[0] > now) {
      arr.shift();
      return arr;
    }
    console.log('[Info] User Token Invalid');
    return [];
  } catch (e) {
    console.log('[Info] User Token parse_jump_token Error', token, e);
    return [];
  }
}

// 发送小程序订阅消息
// https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
async function sendSubscribe({ appid, secret, messages }) {
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.post(
    `https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${accessToken}`,
    messages,
  )
  const data = res.data
  if (errCodes.indexOf(data.errcode) !== -1) {
    await getAccessToken({ appid, secret }, true)
    return await sendSubscribe({ appid, secret, path })
  }
  return data
}


/**
 * @descripttion: 微信OCR印刷体 OCR 识别
 * @param {string} secret 解析 token 的私钥
 * @param {string} appId 待解析的的 token
 * @param {number} len 解析出来的 token arr 的长度
 * @return {array} [name, departmentId, position, wxId]
 */
async function genOcrImage({ appid, secret }, imgUrl) {
  const accessToken = await getAccessToken({ appid, secret })
  const res = await axios.post(
    `https://api.weixin.qq.com/cv/ocr/comm?access_token=${accessToken}&img_url=${imgUrl}`,
  )
  const data = res.data

  return data

}

module.exports = {
  getAccessToken,
  getWechatAuthData,
  getOfficialAuthData,
  getPhoneNumber,
  genQRCodeTicket,
  getOffiaccountUserInfo,
  genUrlLink,
  genMiniprogramQRCode,
  genMiniprogramACode,
  getOffiaccountJSTicket,
  parseJumpToken,
  sendSubscribe,
  genOcrImage
}
