const { createDefaultRoutes } = require('accel-utils')
module.exports = {
  'routes': [
    ...createDefaultRoutes({
      basePath: '/baskets',
      controller: 'basket'
    }),
    {
      'method': 'GET',
      'path': '/baskets/actions/getQuestions',
      'handler': 'basket.getQuestions',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/baskets/actions/addQuestions',
      'handler': 'basket.addQuestions',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/baskets/actions/deleteQuestions',
      'handler': 'basket.deleteQuestions',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/baskets/actions/getBasket',
      'handler': 'basket.getBasket',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/baskets/actions/deleteBasket',
      'handler': 'basket.deleteBasket',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 细目表
    ...createDefaultRoutes({
      basePath: '/tw-specifications',
      controller: 'tw-specification'
    }),
    {
      'method': 'GET',
      'path': '/tw-specifications/actions/getSysList',
      'handler': 'tw-specification.getSysList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/tw-specifications/actions/getUserList',
      'handler': 'tw-specification.getUserList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/tw-specifications/actions/:table_id/getTableDetail',
      'handler': 'tw-specification.getTableDetail',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/tw-specifications/actions/postKnowledge',
      'handler': 'tw-specification.postKnowledge',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/tw-specifications/actions/createTable',
      'handler': 'tw-specification.createTable',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/tw-specifications/actions/:table_id/updateTable',
      'handler': 'tw-specification.updateTable',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/tw-specifications/actions/:table_id/deleteTable',
      'handler': 'tw-specification.deleteTable',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/tw-specifications/actions/downloadTable',
      'handler': 'tw-specification.downloadTable',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/tw-specifications/actions/:id/getTableByExampaper',
      'handler': 'tw-specification.getTableByExampaper',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/tw-specifications/actions/createPaper',
      'handler': 'tw-specification.createPaper',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 试卷试题模板
    ...createDefaultRoutes({
      basePath: '/exampaper-question-templates',
      controller: 'exampaper-question-template'
    }),
    {
      'method': 'GET',
      'path': '/exampaper-question-templates/actions/getUserList',
      'handler': 'exampaper-question-template.getUserList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/exampaper-question-templates/actions/createTemplate',
      'handler': 'exampaper-question-template.createTemplate',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/exampaper-question-templates/actions/:id/deleteTemplateById',
      'handler': 'exampaper-question-template.deleteTemplateById',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 组卷
    ...createDefaultRoutes({
      basePath: '/exampapers',
      controller: 'exampaper'
    }),
    {
      'method': 'POST',
      'path': '/exampapers/actions/knowledgePaper',
      'handler': 'exampaper.knowledgePaper',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/exampapers/actions/:id/paperToPaper',
      'handler': 'exampaper.paperToPaper',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/exampapers/actions/savePaper',
      'handler': 'exampaper.savePaper',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/exampapers/actions/:id/getDetail',
      'handler': 'exampaper.getDetail',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/exampapers/actions/download',
      'handler': 'exampaper.download',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/exampapers/actions/getDtkGateway',
      'handler': 'exampaper.getDtkGateway',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/exampapers/actions/:id/deletePaper',
      'handler': 'exampaper.deletePaper',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/exampapers/actions/getExampaperList',
      'handler': 'exampaper.getExampaperList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/exampapers/actions/search',
      'handler': 'exampaper.search',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/exampapers/actions/getAllRef',
      'handler': 'exampaper.getAllRef',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/exampapers/actions/addByUpload',
      'handler': 'exampaper.addByUpload',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/exampapers/actions/:id/refToPerson',
      'handler': 'exampaper.refToPerson',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/exampapers/actions/:id/updateShareStatus',
      'handler': 'exampaper.updateShareStatus',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
  ]
}
