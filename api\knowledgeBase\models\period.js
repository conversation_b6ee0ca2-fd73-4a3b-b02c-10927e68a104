
module.exports = {
  collectionName: 'period',
  info: {
    name: 'period',
    label: '学段',
    description: '学段'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { key: 1 }, options: { unique: true, sparse: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '学段',
      type: 'string',
      required: true,
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'manual',
      options: [
        {
          label: '同步',
          value: 'sync'
        },
        {
          label: '手动',
          value: 'manual'
        }
      ]
    },
    key: {
      label: 'key', // name-branch
      required: true,
      type: 'string',
    },
    subjects: { // 虚拟字段，库里没有
      label: '学科',
      collection: 'subject',
      // via: 'period',
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
