module.exports = {
  collectionName: 'download-record',
  info: {
    name: 'download-record',
    label: '用户下载记录',
    description: '用户下载记录'
  },
  options: {
    timestamps: true,
    // indexes: [
    //   { keys: { key: 1 }, options: { unique: true } },
    // ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '资源名称',
      type: 'string',
      required: true,
    },
    type: {
      label: '类型',
      type: 'string',
      required: true,
    },
    period: {
      label: '学段',
      model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      model: 'subject',
      required: true,
    },
    source: {
      label: '来源',
      type: 'string',
      required: true,
      options: [
        {
          label: '个人组卷',
          value: 'exampaper'
        },
        {
          label: '备课资源',
          value: 'resource'
        },
        {
          label: '系统试卷',
          value: 'sys_exampaper'
        }
      ]
    },
    source_id: {
      label: '资源ID',
      type: 'string',
      required: true,
    },
    url: {
      label: '资源地址',
      type: 'string',
    },
    suffix: {
      label: '后缀',
      type: 'string'
    },
    user: {
      label: '用户',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0
    }
  }
}
