/**
 * 快速测试解析任务ZIP下载功能
 */

const axios = require('axios')

// 配置
const config = {
  baseURL: 'http://localhost:1337',
  token: 'your-jwt-token-here', // 替换为实际的JWT token
  taskId: '6862692570f8790d701dbc2b' // 使用你提到的实际任务ID
}

async function quickTest() {
  console.log('🧪 快速测试解析任务ZIP下载功能')
  console.log('='.repeat(50))

  try {
    console.log('📋 测试参数:')
    console.log(`  服务器: ${config.baseURL}`)
    console.log(`  任务ID: ${config.taskId}`)
    console.log(`  Token: ${config.token.substring(0, 20)}...`)
    console.log('')

    console.log('🚀 发送请求...')
    const startTime = Date.now()

    const response = await axios.post(
      `${config.baseURL}/parse-tasks/actions/downloadZip`,
      {
        id: config.taskId,
        zipName: 'test-download.zip',
        timeout: 30000
      },
      {
        responseType: 'stream',
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Content-Type': 'application/json'
        },
        timeout: 35000
      }
    )

    const endTime = Date.now()

    console.log('✅ 请求成功!')
    console.log(`  HTTP状态: ${response.status}`)
    console.log(`  状态文本: ${response.statusText}`)
    console.log(`  响应时间: ${endTime - startTime}ms`)
    console.log(`  Content-Type: ${response.headers['content-type']}`)
    console.log(`  Content-Disposition: ${response.headers['content-disposition']}`)

    // 验证状态码
    if (response.status === 200) {
      console.log('  ✅ 状态码正确 (200)')
    } else {
      console.log(`  ⚠️  状态码异常: ${response.status}`)
    }

    // 计算响应大小
    let totalSize = 0
    response.data.on('data', chunk => {
      totalSize += chunk.length
    })

    response.data.on('end', () => {
      console.log(`  文件大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`)
      console.log('')
      console.log('🎉 测试完成! ZIP文件流接收成功')
      console.log('')
      console.log('💡 提示:')
      console.log('  - 如果看到这个消息，说明接口没有出现循环引用错误')
      console.log('  - 实际使用时，需要将流保存到文件或直接返回给客户端')
      console.log('  - 请确保替换config中的token和taskId为实际值')
    })

    response.data.on('error', (err) => {
      console.error('❌ 流处理错误:', err.message)
    })

  } catch (error) {
    console.log('❌ 测试失败')
    
    if (error.response) {
      console.log(`  HTTP状态: ${error.response.status}`)
      console.log(`  错误信息: ${JSON.stringify(error.response.data)}`)
    } else if (error.code === 'ECONNABORTED') {
      console.log(`  错误类型: 请求超时`)
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`  错误类型: 连接被拒绝，请确保服务器正在运行`)
    } else {
      console.log(`  错误信息: ${error.message}`)
    }
    
    console.log('')
    console.log('🔧 故障排除:')
    console.log('  1. 确保服务器正在运行 (npm run develop)')
    console.log('  2. 检查JWT token是否有效')
    console.log('  3. 确认解析任务ID存在且有权限访问')
    console.log('  4. 检查任务是否有关联的文件')
  }
}

// 如果直接运行此文件
if (require.main === module) {
  quickTest().catch(console.error)
}

module.exports = { quickTest }
