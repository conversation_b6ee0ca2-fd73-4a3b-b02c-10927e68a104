{"name": "template-serv", "private": true, "version": "0.1.0", "description": "快速开发平台模板", "scripts": {"preinstall": "git submodule update --init --recursive --force", "develop-local-dev": "cross-env DATABASE=local SERVER=test strapi develop", "develop-local-test": "cross-env DATABASE=test SERVER=test strapi develop", "develop-local-start": "cross-env DATABASE=local SERVER=test node bin/www", "dev": "cross-env DATABASE=local SERVER=local node bin/www", "dev-test": "cross-env DATABASE=test SERVER=test node bin/www", "start": "cross-env NODE_ENV=production node bin/www"}, "workspaces": ["packages/strapi", "packages/accel-utils", "plugins/*"], "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^8.5.6", "cross-env": "^7.0.3", "exceljs": "^4.3.0", "jest": "^29.5.0"}, "dependencies": {"archiver": "^7.0.1", "axios": "1.7.4", "bcryptjs": "^2.4.3", "decompress": "^4.2.1", "decompress-unzip": "^4.0.1", "enfscopy": "^1.0.1", "grant-koa": "5.4.8", "joi": "^17.13.3", "jsonwebtoken": "9.0.0", "knex": "2.4.0", "koa-body": "^4.2.0", "koa2-ratelimit": "^0.9.0", "lodash": "^4.17.21", "moment": "^2.30.1", "mongodb": "5.8.0", "mysql": "^2.18.1", "node-xlsx": "^0.24.0", "qs": "^6.14.0", "rimraf": "^5.0.10", "tencentcloud-sdk-nodejs": "^4.0.276", "ua-parser-js": "1.0.33", "underscore": "^1.13.7", "url": "^0.11.4", "uuid": "^3.1.0", "wechatpay-node-v3": "^1.3.0", "xml2js": "0.5.0", "zip-dir": "^2.0.0"}, "engines": {"node": ">=16.20.0", "npm": ">=6.0.0"}, "license": "MIT"}