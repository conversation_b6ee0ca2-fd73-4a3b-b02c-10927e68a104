<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量下载ZIP功能演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        textarea {
            height: 150px;
            resize: vertical;
            font-family: monospace;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .example-urls {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .example-urls h4 {
            margin-top: 0;
            color: #666;
        }
        
        .example-urls pre {
            background-color: white;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
            display: none;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>批量下载ZIP功能演示</h1>
        
        <form id="downloadForm">
            <div class="form-group">
                <label for="urls">文件URL列表 (每行一个URL，或使用JSON格式):</label>
                <textarea id="urls" placeholder="输入URL列表，例如：
https://httpbin.org/json
https://httpbin.org/xml
https://httpbin.org/html

或者使用JSON格式指定自定义文件名：
[
  {&quot;url&quot;: &quot;https://httpbin.org/json&quot;, &quot;filename&quot;: &quot;数据.json&quot;},
  {&quot;url&quot;: &quot;https://httpbin.org/xml&quot;, &quot;filename&quot;: &quot;配置.xml&quot;},
  &quot;https://httpbin.org/html&quot;
]"></textarea>
            </div>
            
            <div class="form-group">
                <label for="zipName">ZIP文件名:</label>
                <input type="text" id="zipName" value="downloaded-files.zip" placeholder="例如: my-files.zip">
            </div>
            
            <div class="form-group">
                <label for="timeout">超时时间 (毫秒):</label>
                <input type="number" id="timeout" value="30000" min="1000" max="300000" step="1000">
            </div>
            
            <div class="form-group">
                <label for="serverUrl">服务器地址:</label>
                <input type="text" id="serverUrl" value="http://localhost:1337" placeholder="例如: http://localhost:1337">
            </div>
            
            <button type="submit" id="downloadBtn">开始下载</button>
            <button type="button" id="clearBtn">清空</button>
            <button type="button" id="exampleBtn">加载示例</button>
            
            <div class="progress" id="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div class="status" id="status"></div>
        </form>
        
        <div class="example-urls">
            <h4>示例URL格式:</h4>
            <pre id="exampleJson">[
  {
    "url": "https://httpbin.org/json",
    "filename": "测试数据.json"
  },
  {
    "url": "https://httpbin.org/xml",
    "filename": "配置文件.xml"
  },
  "https://httpbin.org/html"
]</pre>
        </div>
    </div>

    <script>
        const form = document.getElementById('downloadForm');
        const urlsTextarea = document.getElementById('urls');
        const zipNameInput = document.getElementById('zipName');
        const timeoutInput = document.getElementById('timeout');
        const serverUrlInput = document.getElementById('serverUrl');
        const downloadBtn = document.getElementById('downloadBtn');
        const clearBtn = document.getElementById('clearBtn');
        const exampleBtn = document.getElementById('exampleBtn');
        const status = document.getElementById('status');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');

        // 显示状态消息
        function showStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        // 隐藏状态消息
        function hideStatus() {
            status.style.display = 'none';
        }

        // 显示进度条
        function showProgress() {
            progress.style.display = 'block';
            progressBar.style.width = '0%';
        }

        // 隐藏进度条
        function hideProgress() {
            progress.style.display = 'none';
        }

        // 更新进度条
        function updateProgress(percent) {
            progressBar.style.width = percent + '%';
        }

        // 解析URL输入
        function parseUrls(input) {
            const trimmed = input.trim();
            
            if (!trimmed) {
                throw new Error('请输入URL列表');
            }
            
            // 尝试解析为JSON
            if (trimmed.startsWith('[') || trimmed.startsWith('{')) {
                try {
                    return JSON.parse(trimmed);
                } catch (e) {
                    throw new Error('JSON格式错误: ' + e.message);
                }
            }
            
            // 按行分割
            return trimmed.split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0);
        }

        // 表单提交处理
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            try {
                hideStatus();
                showProgress();
                downloadBtn.disabled = true;
                downloadBtn.textContent = '下载中...';
                
                // 解析URL
                const urls = parseUrls(urlsTextarea.value);
                
                if (urls.length === 0) {
                    throw new Error('请至少输入一个URL');
                }
                
                if (urls.length > 100) {
                    throw new Error('最多支持100个文件');
                }
                
                updateProgress(10);
                
                // 准备请求数据
                const requestData = {
                    urls: urls,
                    zipName: zipNameInput.value || 'files.zip',
                    timeout: parseInt(timeoutInput.value) || 30000
                };
                
                updateProgress(20);
                
                // 发送请求
                const response = await fetch(serverUrlInput.value + '/upload/external/batch-download-zip', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                updateProgress(50);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '下载失败');
                }
                
                updateProgress(80);
                
                // 获取文件blob
                const blob = await response.blob();
                
                updateProgress(90);
                
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = requestData.zipName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                updateProgress(100);
                
                showStatus(`成功下载 ${urls.length} 个文件到 ${requestData.zipName}`, 'success');
                
            } catch (error) {
                console.error('下载错误:', error);
                showStatus('下载失败: ' + error.message, 'error');
            } finally {
                downloadBtn.disabled = false;
                downloadBtn.textContent = '开始下载';
                setTimeout(hideProgress, 2000);
            }
        });

        // 清空按钮
        clearBtn.addEventListener('click', () => {
            urlsTextarea.value = '';
            zipNameInput.value = 'downloaded-files.zip';
            timeoutInput.value = '30000';
            hideStatus();
            hideProgress();
        });

        // 示例按钮
        exampleBtn.addEventListener('click', () => {
            urlsTextarea.value = `[
  {
    "url": "https://httpbin.org/json",
    "filename": "测试数据.json"
  },
  {
    "url": "https://httpbin.org/xml",
    "filename": "配置文件.xml"
  },
  "https://httpbin.org/html"
]`;
            zipNameInput.value = 'example-files.zip';
        });
    </script>
</body>
</html>
