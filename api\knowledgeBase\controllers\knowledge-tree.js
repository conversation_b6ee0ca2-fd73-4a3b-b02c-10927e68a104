'use strict'
const { BranchCurdRouter } = require('accel-utils')
const axios = require('axios')
const _ = require('lodash')
const { ObjectId } = require('mongodb')

const { replaceChapterField, getTreeBulkWriteArray } = require('../services/tree')

const defaultTreeName = '默认知识树'
const branchCurdRouter = new (class extends BranchCurdRouter {
  async branchFind (ctx) {
    const { query } = this._parseBranchCtx(ctx)
    const branchId = ctx.state.user.pBranch?.id
    const userId = ctx.state.user.id

    // 如果学段学科下没有知识树，则创建默认知识树
    if (query.subject && query.period) {
      let knowledgeTree = await strapi.query('knowledge-tree').findOne({ subject: query.subject, period: query.period, pBranch: branchId }, [])
      if (!knowledgeTree) {
        // const chapterId = new ObjectId()
        await strapi.query('knowledge-tree').create({
          name: defaultTreeName,
          period: query.period,
          subject: query.subject,
          key: `${defaultTreeName}-${query.subject}-${branchId}`,
          operator: userId,
          creator: userId,
          pBranch: branchId,
          children: [],
        })
      }
    }
    return super.branchFind(ctx)
  }

  async branchUpdate (ctx) {
    const { params, data } = this._parseBranchCtx(ctx)
    const branchId = ctx.state.user.pBranch?.id
    const userId = ctx.state.user.id
    if (data.name) {
      const knowledgeTree = await strapi.query('knowledge-tree').findOne({ id: params.id, }, [])
      data.key = `${data.name}-${knowledgeTree.subject}-${branchId}`
    }
    data.operator = userId
    return super.branchUpdate(ctx)
  }
})('knowledge-tree')

// 获取学校教材树
async function getKnowledgeTreeByPeriodSubject (ctx) {
  let { subject, period, needKnowledge = true } = ctx.request.query
  const branchId = ctx.state.user.pBranch?.id
  const userId = ctx.state.user.id
  if (!subject || !period) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  if (!branchId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户异常')
  }
  if (needKnowledge === 'false') needKnowledge = false

  let knowledgeTree = await strapi.query('knowledge-tree').findOne({ subject: subject, period: period, pBranch: branchId }, [])
  if (!knowledgeTree) {
    knowledgeTree = await strapi.query('knowledge-tree').create({
      name: defaultTreeName,
      period: period,
      subject: subject,
      key: `${defaultTreeName}-${subject}-${branchId}`,
      operator: userId,
      creator: userId,
      pBranch: branchId,
      children: [],
    })
    return ctx.wrapper.succ(knowledgeTree)
  }

  let knowledgeTreeChapters = await strapi.query('knowledge-tree-chapter').find({
    knowledge_tree: knowledgeTree.id,
    _limit: -1,
  }, needKnowledge ? ['knowledges'] : [])


  const knowledgeTreeChapterMap = Object.fromEntries(
    knowledgeTreeChapters.map(value => [value.id, value])
  )

  replaceChapterField(knowledgeTree.children, knowledgeTreeChapterMap, needKnowledge)
  // 更新树层级
  // await strapi.query('knowledge-tree').update({ id: id }, { children: knowledgeTree.children })
  const notMatchIds = Object.values(knowledgeTreeChapterMap).filter(e => e.match !== true).map(e => e.id)
  if (notMatchIds.length > 0) {
    console.log(`not match ids ${notMatchIds.length}:`, notMatchIds)
    await strapi.query('knowledge-tree-chapter').model.deleteMany({
      _id: { $in: notMatchIds },
    })
  }
  return ctx.wrapper.succ(knowledgeTree)
}

// 更新学校教材树
async function syncKnowledgeTreeById (ctx) {
  let { id, children } = ctx.request.body
  const userId = ctx.state.user.id
  const branchId = ctx.state.user.pBranch?.id
  // 参数检测和数据准备
  if (!branchId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户异常')
  }
  if (!id || !children || !Array.isArray(children)) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  let knowledgeTree = await strapi.query('knowledge-tree').findOne({ id, pBranch: branchId, }, [])
  if (!knowledgeTree) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  const knowledges = await strapi.query('knowledge').find({
    period: knowledgeTree.period,
    subject: knowledgeTree.subject,
    pBranch: branchId,
    _limit: -1,
  }, [])
  const knowledgeMap = Object.fromEntries(
    knowledges.map(value => [value.name, value])
  )

  // 共性更新数据
  const updateData = {
    period: knowledgeTree.period,
    subject: knowledgeTree.subject,
    knowledge_tree: knowledgeTree.id,
    operator: userId,
    creator: userId,
    pBranch: branchId,
  }
  // 构造章节根路径
  let curPath = `${knowledgeTree.period}-${knowledgeTree.subject}-${knowledgeTree.id}`
  knowledgeTree.children = children
  await getTreeBulkWriteArray(knowledgeTree, curPath, updateData, knowledgeMap, 'knowledge-tree', 'knowledge-tree-chapter')
  return ctx.wrapper.succ({})
}

module.exports = {
  getKnowledgeTreeByPeriodSubject,
  syncKnowledgeTreeById,
  ...branchCurdRouter.createHandlers(),
}