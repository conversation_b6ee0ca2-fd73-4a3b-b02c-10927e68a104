'use strict'

const _ = require('lodash')
const enums = require('../../common/enums')

/**
 * 获取校本教辅统计信息
 * @param {object} user - 用户对象
 * @param {object} filters - 过滤条件
 * @returns {Promise<object>} 统计信息
 */
async function getTextbookStatistics(user, filters = {}) {
  const baseQuery = {
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    ...filters
  }

  // 总数统计
  const totalCount = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count(baseQuery)

  // 按状态统计
  const statusStats = await Promise.all([
    strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count({ ...baseQuery, status: 'draft' }),
    strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count({ ...baseQuery, status: 'published' }),
    strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count({ ...baseQuery, status: 'archived' }),
  ])

  // 按学段统计
  const periodStats = await Promise.all([
    strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count({ ...baseQuery, period: '小学' }),
    strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count({ ...baseQuery, period: '初中' }),
    strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count({ ...baseQuery, period: '高中' }),
  ])

  // 按科目统计（取前5个）
  const subjects = ['语文', '数学', '英语', '物理', '化学']
  const subjectStats = await Promise.all(
    subjects.map(subject => 
      strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count({ ...baseQuery, subject })
    )
  )

  return {
    total: totalCount,
    status: {
      draft: statusStats[0],
      published: statusStats[1],
      archived: statusStats[2]
    },
    period: {
      primary: periodStats[0],
      middle: periodStats[1],
      high: periodStats[2]
    },
    subject: subjects.reduce((acc, subject, index) => {
      acc[subject] = subjectStats[index]
      return acc
    }, {})
  }
}

/**
 * 获取推荐的校本教辅
 * @param {object} user - 用户对象
 * @param {number} limit - 限制数量
 * @returns {Promise<Array>} 推荐教辅列表
 */
async function getFeaturedTextbooks(user, limit = 10) {
  return await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).find({
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    status: 'published',
    is_featured: true,
    _sort: 'sort_order:desc,view_times:desc,createdAt:desc',
    _limit: limit
  }, ['creator', 'cover_file'])
}

/**
 * 获取热门校本教辅
 * @param {object} user - 用户对象
 * @param {number} limit - 限制数量
 * @returns {Promise<Array>} 热门教辅列表
 */
async function getPopularTextbooks(user, limit = 10) {
  return await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).find({
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    status: 'published',
    _sort: 'view_times:desc,download_times:desc,createdAt:desc',
    _limit: limit
  }, ['creator', 'cover_file'])
}

/**
 * 获取最新校本教辅
 * @param {object} user - 用户对象
 * @param {number} limit - 限制数量
 * @returns {Promise<Array>} 最新教辅列表
 */
async function getLatestTextbooks(user, limit = 10) {
  return await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).find({
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    status: 'published',
    _sort: 'publish_time:desc,createdAt:desc',
    _limit: limit
  }, ['creator', 'cover_file'])
}

/**
 * 根据用户偏好推荐校本教辅
 * @param {object} user - 用户对象
 * @param {object} preferences - 用户偏好 {grade, subject, period}
 * @param {number} limit - 限制数量
 * @returns {Promise<Array>} 推荐教辅列表
 */
async function getRecommendedTextbooks(user, preferences = {}, limit = 10) {
  const query = {
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    status: 'published',
    _sort: 'view_times:desc,download_times:desc,createdAt:desc',
    _limit: limit
  }

  // 根据偏好添加过滤条件
  if (preferences.grade) {
    query.grade = preferences.grade
  }
  if (preferences.subject) {
    query.subject = preferences.subject
  }
  if (preferences.period) {
    query.period = preferences.period
  }

  return await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).find(query, ['creator', 'cover_file'])
}

/**
 * 搜索校本教辅
 * @param {object} user - 用户对象
 * @param {string} keyword - 搜索关键词
 * @param {object} filters - 过滤条件
 * @param {object} pagination - 分页参数
 * @returns {Promise<object>} 搜索结果
 */
async function searchTextbooks(user, keyword, filters = {}, pagination = {}) {
  const { page = 1, pageSize = 20 } = pagination
  const start = (page - 1) * pageSize

  let query = {
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    status: 'published',
    ...filters
  }

  // 关键词搜索
  if (keyword && keyword.trim()) {
    query._where = {
      _or: [
        { name_contains: keyword.trim() },
        { description_contains: keyword.trim() },
        { tags_contains: keyword.trim() }
      ]
    }
  }

  const [textbooks, total] = await Promise.all([
    strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).find({
      ...query,
      _start: start,
      _limit: pageSize,
      _sort: 'is_top:desc,sort_order:desc,view_times:desc,createdAt:desc'
    }, ['creator', 'cover_file']),
    strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).count(query)
  ])

  return {
    data: textbooks,
    pagination: {
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 复制校本教辅
 * @param {string} textbookId - 教辅ID
 * @param {object} user - 用户对象
 * @param {object} overrides - 覆盖数据
 * @returns {Promise<object>} 新的教辅对象
 */
async function duplicateTextbook(textbookId, user, overrides = {}) {
  const originalTextbook = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).findOne({
    id: textbookId,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO
  })

  if (!originalTextbook) {
    throw new Error('原教辅不存在或无权限访问')
  }

  // 准备复制数据
  const duplicateData = {
    name: `${originalTextbook.name} - 副本`,
    description: originalTextbook.description,
    grade: originalTextbook.grade,
    period: originalTextbook.period,
    subject: originalTextbook.subject,
    cover_url: originalTextbook.cover_url,
    cover_file: originalTextbook.cover_file,
    disk_files: originalTextbook.disk_files || [],
    file_count: originalTextbook.file_count,
    total_size: originalTextbook.total_size,
    category: originalTextbook.category,
    tags: originalTextbook.tags || [],
    status: 'draft', // 复制的教辅默认为草稿状态
    is_featured: false,
    is_top: false,
    sort_order: 0,
    remark: originalTextbook.remark,
    creator: user.id,
    operator: user.id,
    pBranch: user.pBranch.id,
    ...overrides
  }

  return await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).create(duplicateData)
}

/**
 * 批量导入校本教辅
 * @param {Array} textbooksData - 教辅数据数组
 * @param {object} user - 用户对象
 * @returns {Promise<object>} 导入结果
 */
async function batchImportTextbooks(textbooksData, user) {
  const results = {
    success: [],
    failed: []
  }

  for (const data of textbooksData) {
    try {
      const textbookData = {
        ...data,
        creator: user.id,
        operator: user.id,
        pBranch: user.pBranch.id,
        status: data.status || 'draft'
      }

      const textbook = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).create(textbookData)
      results.success.push(textbook)
    } catch (error) {
      results.failed.push({
        data,
        error: error.message
      })
    }
  }

  return results
}

module.exports = {
  getTextbookStatistics,
  getFeaturedTextbooks,
  getPopularTextbooks,
  getLatestTextbooks,
  getRecommendedTextbooks,
  searchTextbooks,
  duplicateTextbook,
  batchImportTextbooks
}
