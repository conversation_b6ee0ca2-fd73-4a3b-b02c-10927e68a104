# 校本教辅 API 文档

## 模型字段说明

### 基本信息
- `name` - 教辅名称 (必填，最大200字符)
- `description` - 教辅描述 (可选)
- `grade` - 年级 (必填，如：一年级、二年级...高三)
- `period` - 学段 (必填，小学/初中/高中)
- `subject` - 科目 (必填，语文/数学/英语等)

### 文件相关
- `cover_url` - 封面URL (可选)
- `cover_file` - 封面文件ID (可选，关联disk-file)
- `disk_files` - 网盘文件ID数组 (关联disk-file)
- `file_count` - 文件数量 (自动计算)
- `total_size` - 总文件大小(字节) (自动计算)

### 分类和标签
- `category` - 教辅类别 (练习册/试卷集/教学课件等)
- `tags` - 标签数组

### 状态管理
- `status` - 状态 (draft/published/archived)
- `publish_time` - 发布时间 (自动设置)
- `is_featured` - 是否推荐
- `is_top` - 是否置顶
- `sort_order` - 排序权重

### 统计信息
- `view_times` - 浏览次数
- `download_times` - 下载次数

### 系统字段
- `creator` - 创建人
- `operator` - 最后操作人
- `pBranch` - 租户
- `deleted` - 删除标识

## API 接口

### 1. 基础 CRUD 操作

#### 获取校本教辅列表
```
GET /school-textbooks
```

**查询参数:**
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `grade` - 年级过滤
- `period` - 学段过滤
- `subject` - 科目过滤
- `category` - 类别过滤
- `status` - 状态过滤
- `is_featured` - 是否推荐
- `is_top` - 是否置顶
- `search` - 搜索关键词

**响应示例:**
```json
{
  "code": 200,
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "name": "三年级数学练习册",
      "description": "适合三年级学生的数学练习",
      "grade": "三年级",
      "period": "小学",
      "subject": "数学",
      "category": "练习册",
      "status": "published",
      "file_count": 5,
      "total_size": 10485760,
      "view_times": 100,
      "download_times": 50,
      "cover_file": {
        "id": "507f1f77bcf86cd799439012",
        "name": "cover.jpg",
        "url": "https://example.com/cover.jpg"
      },
      "creator": {
        "id": "507f1f77bcf86cd799439013",
        "username": "teacher1"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

#### 获取单个校本教辅
```
GET /school-textbooks/:id
```

#### 删除校本教辅
```
DELETE /school-textbooks/:id
```

### 2. 自定义操作

#### 创建校本教辅
```
POST /school-textbooks/actions/create
```

**请求体:**
```json
{
  "name": "三年级数学练习册",
  "description": "适合三年级学生的数学练习",
  "grade": "三年级",
  "period": "小学",
  "subject": "数学",
  "category": "练习册",
  "cover_file": "507f1f77bcf86cd799439012",
  "disk_files": [
    "507f1f77bcf86cd799439013",
    "507f1f77bcf86cd799439014"
  ],
  "tags": ["数学", "练习", "三年级"],
  "status": "draft",
  "remark": "备注信息"
}
```

#### 更新校本教辅
```
PUT /school-textbooks/actions/:id/update
```

#### 批量操作
```
POST /school-textbooks/actions/batch
```

**请求体:**
```json
{
  "ids": [
    "507f1f77bcf86cd799439011",
    "507f1f77bcf86cd799439012"
  ],
  "operation": "publish"
}
```

**支持的操作:**
- `publish` - 发布
- `archive` - 归档
- `delete` - 删除
- `feature` - 设为推荐
- `unfeature` - 取消推荐
- `top` - 置顶
- `untop` - 取消置顶

#### 增加浏览次数
```
POST /school-textbooks/actions/:id/view
```

#### 增加下载次数
```
POST /school-textbooks/actions/:id/download
```

## 使用示例

### JavaScript 示例

```javascript
// 创建校本教辅
async function createTextbook() {
  const response = await fetch('/school-textbooks/actions/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your-jwt-token'
    },
    body: JSON.stringify({
      name: '三年级数学练习册',
      grade: '三年级',
      period: '小学',
      subject: '数学',
      category: '练习册',
      disk_files: ['file-id-1', 'file-id-2'],
      tags: ['数学', '练习'],
      status: 'published'
    })
  })
  
  const result = await response.json()
  console.log('创建成功:', result.data)
}

// 获取推荐教辅
async function getFeaturedTextbooks() {
  const response = await fetch('/school-textbooks?is_featured=true&status=published', {
    headers: {
      'Authorization': 'Bearer your-jwt-token'
    }
  })
  
  const result = await response.json()
  console.log('推荐教辅:', result.data)
}

// 搜索教辅
async function searchTextbooks(keyword) {
  const response = await fetch(`/school-textbooks?search=${encodeURIComponent(keyword)}`, {
    headers: {
      'Authorization': 'Bearer your-jwt-token'
    }
  })
  
  const result = await response.json()
  console.log('搜索结果:', result.data)
}

// 批量发布
async function batchPublish(ids) {
  const response = await fetch('/school-textbooks/actions/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your-jwt-token'
    },
    body: JSON.stringify({
      ids: ids,
      operation: 'publish'
    })
  })
  
  const result = await response.json()
  console.log('批量发布结果:', result.data)
}
```

### cURL 示例

```bash
# 创建校本教辅
curl -X POST http://localhost:1337/school-textbooks/actions/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "name": "三年级数学练习册",
    "grade": "三年级",
    "period": "小学",
    "subject": "数学",
    "category": "练习册",
    "status": "published"
  }'

# 获取教辅列表
curl -X GET "http://localhost:1337/school-textbooks?grade=三年级&subject=数学" \
  -H "Authorization: Bearer your-jwt-token"

# 批量操作
curl -X POST http://localhost:1337/school-textbooks/actions/batch \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "ids": ["507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012"],
    "operation": "publish"
  }'
```

## 数据关系

### 与网盘文件的关系
- 校本教辅可以关联多个网盘文件 (`disk_files`)
- 网盘文件可以属于一个校本教辅 (`school_textbook`)
- 支持封面文件单独关联 (`cover_file`)

### 权限控制
- 基于租户 (`pBranch`) 的数据隔离
- 创建人 (`creator`) 权限验证
- 软删除机制 (`deleted` 字段)

### 状态流转
```
draft (草稿) → published (已发布) → archived (已归档)
                    ↓
                deleted (已删除)
```

## 注意事项

1. **文件关联**: 创建或更新教辅时，系统会自动计算 `file_count` 和 `total_size`
2. **发布时间**: 状态从非发布变为发布时，会自动设置 `publish_time`
3. **权限验证**: 所有操作都会验证用户权限和租户隔离
4. **软删除**: 删除操作只是标记 `deleted=1`，不会物理删除数据
5. **搜索优化**: 支持按名称、描述、标签进行模糊搜索
6. **排序规则**: 默认按置顶、排序权重、浏览次数、创建时间排序
