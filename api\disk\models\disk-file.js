module.exports = {
  collectionName: 'disk-file',
  info: {
    name: 'disk-file',
    label: '网盘',
    description: '网盘'
  },
  options: {
    timestamps: true,
    indexes: [
      // { keys: { key: 1 }, options: { unique: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      type: 'string',
      required: true,
    },
    period: {
      label: '学段',
      type: 'string',
      required: true,
    },
    subject: {
      label: '科目',
      type: 'string',
      required: true,
    },
    size: {
      label: '文件大小',
      type: 'number',
      default: 0,
    },
    type: {
      label: '类型',
      type: 'string',
      default: '',
    },
    suffix: {
      label: '后缀名',
      type: 'string',
      default: '',
    },
    category: {
      label: '类别',
      type: 'string',
      default: ''
    },
    hash: {
      label: '文件内容的哈希值',
      type: 'string',
      default: ''
    },
    url: {
      label: '对象存储地址',
      type: 'string',
      default: ''
    },
    is_folder: {
      label: '是否文件夹',
      type: 'number',
      default: 0
    },
    parent_id: {
      label: '父文件夹ID',
      type: 'string',
      default: '0'
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'upload',
      options: [
        {
          label: '上传',
          value: 'upload'
        },
        {
          label: '引用',
          value: 'ref'
        }
      ]
    },
    source_id: {
      label: '来源ID',
      type: 'string',
      default: 'upload',
    },
    source_user: {
      label: '共享人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false
    },
    download_times: {
      label: '下载次数',
      type: 'number',
      default: 0,
      required: true
    },
    view_times: {
      label: '浏览次数',
      type: 'number',
      default: 0,
      required: true
    },
    shared: {
      label: '分享标识',
      type: 'number',
      default: 0,
    },
    is_top: {
      label: '置顶标识',
      type: 'number',
      default: 0,
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '创建人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    school_id: {
      label: '分享标识',
      type: 'number',
      required: true
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
    parse_task: {
      label: '解析任务',
      model: 'parse-task',
    },
    school_textbook: {
      label: '校本教辅',
      model: 'school-textbook',
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0
    }
  }
}
