module.exports = {
  collectionName: 'exampaper-question-template',
  info: {
    name: 'exampaper-question-template',
    label: '试卷试题模板',
    description: '试卷试题模板'
  },
  options: {
    timestamps: true,
    // indexes: [
    //   { keys: { key: 1 }, options: { unique: true } },
    // ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名字',
      type: 'string',
      required: true
    },
    period: {
      label: '学段',
      model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      model: 'subject',
      required: true,
    },
    type: {
      label: '类型',
      type: 'string',
      required: true,
      default: 'sys',
      options: [
        {
          label: '系统',
          value: 'sys'
        },
        {
          label: '用户自定义',
          value: 'custom'
        },
      ]
    },
    blocks: {
      label: '模块',
      type: 'json',
      required: true,
      jsonSchema: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            type: {
              title: '类型',
              type: 'string'
            },
            num: {
              title: '标题',
              type: 'integer'
            }
          }
        }
      }
    },
    user: {
      label: '用户',
      plugin: 'users-permissions',
      model: 'user',
      configurable: false
    }
  }
}
