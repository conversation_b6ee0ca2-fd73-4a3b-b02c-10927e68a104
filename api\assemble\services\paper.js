const _ = require('lodash')
const axios = require('axios');
const client = require('../../common/client');
const enums = require('../../common/enums');
const MODEL = 'exampaper';

module.exports = {
  process,
  incTimes
}

function process(exam) {
  let subject = exam.subject || '';
  let volumes = exam.volumes || [];
  if (subject !== '英语') {
    return exam;
  }
  let ques_num = 1;
  for (let volume of volumes) {
    let blocks = volume.blocks || []
    for (let block of blocks) {
      let ques_type = block.type || '';
      let questions = block.questions || []
      for (let question of questions) {
        let ques_blocks = question.blocks || {};
        let stems = ques_blocks.stems || [];
        let description = question.description || '';
        let ques_number = [];
        if (ques_type === '单选题') {
          ques_number.push(ques_num);
          ques_num += 1
        } else if (ques_type === '填空题' || ques_type === '阅读理解') {
          for (let one_stem of stems) {
            ques_number.push(ques_num);
            let regex = /[（\(]\d+?[\)）]/g;
            let result = one_stem.stem.match(regex)
            if (result && result.length !== 0) {
              one_stem.stem = one_stem.stem.replace(result[0], ' ');
            }
            ques_num += 1
          }
        } else if (ques_type === '语法填空' || ques_type === '七选五') {
          let regex = /[（\(]\d+?[\)）]/g;
          let result = stems[0].stem.match(regex)
          if (result) {
            for (let i = 0; i < result.length; i++) {
              ques_number.push(ques_num)
              question.blocks.stems[0].stem = question.blocks.stems[0].stem.replace(result[i], '(' + ques_num + ')');
              ques_num += 1
            }
          }
        } else if (ques_type === '完形填空') {
          // 删除小题号
          for (let one_stem of stems) {
            ques_number.push(ques_num)
            one_stem.stem = ' '
            ques_num += 1
          }
          // 替换试题中的题号
          let regex = /[（\(]\d+?[\)）]/g;
          let result = description.match(regex)
          if (result) {
            for (let i = 0; i < result.length; i++) {
              if (result.length === ques_number.length) {
                question.description = question.description.replace(result[i], '(' + ques_number[i] + ')');
              } else {
                question.description = question.description.replace(result[i], '');
              }
            }
          }
        } else {
          ques_number.push(ques_num);
          ques_num += 1
        }
        question['ques_number'] = ques_number;
      }
    }
  }
}

/**
 * 增加浏览或者下载次数
 * @param id {string} 试卷ID
 * @param action {string} view_times/download_times
 * @returns {Promise<void>}
 */
async function incTimes(id, action) {
  const data = await strapi.query(MODEL).findOne({id});
  if (_.isEmpty(data)) return;
  const update = {
    [action]: data[action] + 1
  };
  await strapi.query(MODEL).update({id}, update);
}
