'use strict'
const { BranchCurdRouter } = require('accel-utils')
const _ = require('lodash')
const client = require('../../common/client')
const enums = require('../../common/enums')
const Joi = require('joi')
const branchCurdRouter = new (class extends BranchCurdRouter {
})(enums.CollectionName.TREE_CATALOG)

module.exports = {
  ...branchCurdRouter.createHandlers(),
  getPeriodCatalog,
  getBookTreeCatalog,
  updatePeriodCatalog,
  updateBookTreeCatalog,
}

async function getPeriodCatalog (ctx) {
  const user = ctx.state.user
  const doc = await strapi.query(enums.CollectionName.TREE_CATALOG).findOne({ pBranch: user.pBranch.id })
  return ctx.wrapper.succ(_.isEmpty(doc) ? [] : doc.periodCatalog)
}
async function getBookTreeCatalog (ctx) {
  const user = ctx.state.user
  const doc = await strapi.query(enums.CollectionName.TREE_CATALOG).findOne({ pBranch: user.pBranch.id })
  return ctx.wrapper.succ(_.isEmpty(doc) ? [] : doc.bookTreeCatalog)
}

const UPDATE_PERIOD_CATALOG = Joi.object({
  children: Joi.array().items(Joi.object({
    key: Joi.string().required().valid('period'),
    name: Joi.string().required(),
    children: Joi.array().items(Joi.object({
      key: Joi.string().required().valid('subject'),
      name: Joi.string().required(),
    }))
  }))
})

async function updatePeriodCatalog (ctx) {
  const { error, value } = UPDATE_PERIOD_CATALOG.validate(ctx.request.body)
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)
  const user = ctx.state.user
  const query = { pBranch: user.pBranch.id }
  const doc = await strapi.query(enums.CollectionName.TREE_CATALOG).findOne(query, [])
  if (_.isEmpty(doc)) {
    await strapi.query(enums.CollectionName.TREE_CATALOG).create({
      periodCatalog: { children: value.children },
      bookTreeCatalog: { children: value.children },
      pBranch: user.pBranch.id,
      creator: user.id,
      operator: user.id
    })
  } else {
    const curBookTreeCatalog = updateBookTreeCatalogByPeriod(value.children, doc.bookTreeCatalog.children)
    await strapi.query(enums.CollectionName.TREE_CATALOG).update({ id: doc.id }, {
      periodCatalog: { children: value.children },
      bookTreeCatalog: { children: curBookTreeCatalog },
      operator: user.id
    })
  }
  return ctx.wrapper.succ('')
}
const UPDATE_BOOK_TREE_CATALOG = Joi.object({
  children: Joi.array().items(Joi.object({
    key: Joi.string().required().valid('period'),
    name: Joi.string().required(),
    children: Joi.array().items(Joi.object({
      key: Joi.string().required().valid('subject'),
      name: Joi.string().required(),
      children: Joi.array().items(Joi.object({
        key: Joi.string().required().valid('press_version'),
        name: Joi.string().required(),
        children: Joi.array().items(Joi.object({
          key: Joi.string().required().valid('grade'),
          name: Joi.string().required(),
          id: Joi.number().required(),
        }))
      }))
    }))
  }))
})

async function updateBookTreeCatalog (ctx) {
  const { error, value } = UPDATE_BOOK_TREE_CATALOG.validate(ctx.request.body)
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)
  const user = ctx.state.user
  const query = { pBranch: user.pBranch.id }
  const doc = await strapi.query(enums.CollectionName.TREE_CATALOG).findOne(query)
  if (_.isEmpty(doc)) {
    return ctx.wrapper.error('HANDLE_ERROR', `数据错误：请先同步学段学科`)
  } else {
    await strapi.query(enums.CollectionName.TREE_CATALOG).update({ id: doc.id }, {
      bookTreeCatalog: { children: value.children },
      operator: user.id
    })
  }
  return ctx.wrapper.succ('')
}

function updateBookTreeCatalogByPeriod (periodChildren, bookTreeChildren) {
  const curBbookTree = _.cloneDeep(periodChildren)
  for (const period of curBbookTree) {
    if (period.key === 'period') {
      const bookTreePeriod = bookTreeChildren?.find(e => e.key === 'period' && e.name === period.name)
      for (const subject of period.children) {
        if (subject.key === 'subject') {
          const bookTreeSubject = bookTreePeriod?.children?.find(e => e.key === 'subject' && e.name === subject.name)
          subject.children = bookTreeSubject?.children || []
        }
      }
    }
  }
  return curBbookTree
}