
const { CurdRouter } = require('accel-utils');
const _ = require('lodash');
const client = require('../../common/client');
const enums = require('../../common/enums');
const studyService = require('../../common/services/study');
const basketService = require('../services/basket');
const MODEL = 'exampaper-question-template';
const curdRouter = new CurdRouter(MODEL);

module.exports = {
  ...curdRouter.createHandlers(),
  getUserList,
  createTemplate,
  deleteTemplateById,
}

// const JOI_LIST = Joi.object({
//   period: Joi.string().required(), //  学段
//   subject: Joi.string().required(), // 科目
// });

async function getUserList(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.query;
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  const list = await strapi.query(MODEL).find({
    user: user.id,
    period: studyInfo.period,
    subject: studyInfo.subject,
  });
  if (_.size(list)) {
    list.forEach(e => {
      e.period = params.period;
      e.subject = params.subject;
      delete e._id;
      delete e.user;
      delete e.createdAt;
      delete e.updatedAt;
      delete e['__v'];
    });
  }
  return ctx.wrapper.succ(list);
}

// const JOI_CREATE = Joi.object({
//   name: Joi.string().required(), // 名称
//   period: Joi.string().required(), //  学段
//   subject: Joi.string().required(), // 科目
//   blocks: Joi.array().items(Joi.object({
//     type: Joi.string().required(),
//     num: Joi.number().required(),
//   })).required().min(1),
// });

async function createTemplate (ctx) {
  const user = ctx.state.user;
  const params = ctx.request.body;
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  params.type = enums.PaperTemplateType.CUSTOM;
  params.user = user.id;
  params.period = studyInfo.period;
  params.subject = studyInfo.subject;
  const doc = await strapi.query(MODEL).create(params);
  return ctx.wrapper.succ({template_id: doc.id});
}

// const JOI_DELETE_BY_ID = Joi.object({
//   template_id: Joi.string().required(), //  模板ID
// });

async function deleteTemplateById (ctx) {
  const {id} = ctx.params;
  const user = ctx.state.user;
  await strapi.query(MODEL).delete({user: user.id, id: id});
  return ctx.wrapper.succ({template_id: id});
}
