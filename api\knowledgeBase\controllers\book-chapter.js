'use strict'
const { BranchCurdRouter } = require('accel-utils')
const axios = require('axios')
const _ = require('lodash')
const { deleteLastLevelByIds } = require('../services/tree')

const branchCurdRouter = new (class extends BranchCurdRouter {

  async branchDelete (ctx) {
    const { params } = this._parseBranchCtx(ctx)
    const bookChapter = await strapi.query('book-chapter').findOne({ id: params.id, }, ['book'])
    if (!bookChapter || bookChapter.book.children.length === 0) {
      return ctx.wrapper.error('HANDLE_ERROR', 'bookChapter not found')
    }
    let paths = bookChapter.path.split('-').slice(4)
    deleteLastLevelByIds(bookChapter.book, paths)
    await strapi.query('book').update({ id: bookChapter.book.id, }, { children: bookChapter.book.children })
    return super.branchDelete(ctx)
  }

  async branchUpdate (ctx) {
    const { params, data } = this._parseBranchCtx(ctx)
    const userId = ctx.state.user.id
    if (data.name) {
      const chapter = await strapi.query('book-chapter').findOne({ id: params.id, }, [])
      data.key = chapter.path.replace(chapter.id, data.name)
    }
    data.operator = userId
    return super.branchUpdate(ctx)
  }
})('book-chapter')

module.exports = {
  ...branchCurdRouter.createHandlers(),
}