module.exports = {
  collectionName: 'subject',
  info: {
    name: 'subject',
    label: '学科',
    description: '学科'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { key: 1 }, options: { unique: true, sparse: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '学科',
      type: 'string',
      required: true,
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'manual',
      options: [
        {
          label: '同步',
          value: 'sync'
        },
        {
          label: '手动',
          value: 'manual'
        }
      ]
    },
    key: {
      label: 'key', // name-period
      required: true,
      type: 'string',
    },
    period: {
      label: '学段',
      model: 'period',
      // via: 'subjects',
    },
    press_versions: {
      label: '教材版本',
      collection: 'press-version',
      // via: 'press_version',
    },
    knowledge_tree: {
      label: '知识树',
      model: 'knowledge-tree',
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
