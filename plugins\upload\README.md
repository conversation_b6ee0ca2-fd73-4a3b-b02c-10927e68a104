# 上传与文件管理插件

提供上传与文件管理相关功能，包括：
- 对象存储上传（百度云BOS、腾讯云COS）
- 本地文件上传
- 富文本编辑器文件上传
- **批量下载文件并打包成ZIP** ⭐ 新功能

## 百度云对象存储配置

插件配置文件位置 `/config/plugins.js`

### 配置说明
```typescript
interface UploadConfig {
  objectStorage: {
    // 对象存储类型 bos-百度云 cos-腾讯云
    target: 'bos' | 'cos'
    // 根路径 默认: 'test/'
    baseDir?: string,
    // 默认上传根路径
    // 默认值: 'upload/'
    uploadPath?: string,
    // 富文本上传根路径
    // 默认值: 'upload/rich-text/'
    richTextUploadPath?: string,
    // 对象存储配置 参考下方配置示例
    config:
      { // 百度云对象存储（bos）配置
        AK: string,
        SK: string,
        Bucket: string,
        Endpoint: string
      }
      |
      { // 腾讯云对象存储（cos）配置
        SecretId: string,
        SecretKey: string,
        Bucket: string,
        Region: string,
        Proxy?: string,
        // 有效期
        DurationSeconds?: number,
        AllowPrefix?: string[],
        AllowActions?: string[],
      }
  }
}

```

### 百度云配置示例
```js

module.exports = ({ env }) => ({
  // ...
  upload: {
    objectStorage: {
      target: 'bos',
      config: {
        // Bos Config
        AK: '',
        SK: '',
        Bucket: 'ayx-pk',
        Endpoint: 'https://ayx-pk.bj.bcebos.com'
      },
    },
  }
})
```

### 腾讯云配置示例
```js
module.exports = ({ env }) => ({
  // ...
  upload: {
    objectStorage: {
      target: 'cos',
      config: {
        // COS Config
        SecretId: '',
        SecretKey: '',
        Bucket: '',
        Region: '',
        Proxy: '',
        // 有效期
        DurationSeconds: 1800,
        AllowPrefix: '*',
        AllowActions: ['*'],
      },
    },
  }
})
```

## 批量下载ZIP功能

### 功能介绍

通过提供多个对象存储URL，批量下载文件并将它们打包成一个ZIP文件返回。

### API接口

```
POST /upload/external/batch-download-zip
```

### 请求参数

```json
{
  "urls": [
    "https://example.com/file1.pdf",
    "https://example.com/file2.jpg",
    {
      "url": "https://example.com/file3.docx",
      "filename": "自定义文件名.docx"
    }
  ],
  "zipName": "my-files.zip",
  "timeout": 30000
}
```

### 功能特点

- ✅ 支持批量下载多个文件
- ✅ 自动打包成ZIP格式
- ✅ 支持自定义文件名
- ✅ 并发下载优化（限制并发数为5）
- ✅ 错误处理和重试机制
- ✅ 下载结果摘要
- ✅ 文件名安全处理
- ✅ 超时控制
- ✅ 流式响应，内存友好

### 使用限制

- 最多支持100个文件
- 单个文件默认超时30秒
- 并发下载限制为5个文件

### 详细文档

更多详细信息请参考：
- [批量下载ZIP功能文档](./docs/batch-download-zip.md)
- [使用示例](./examples/batch-download-example.js)
- [在线演示页面](./examples/batch-download-demo.html)
- [测试脚本](./test/batch-download-test.js)
