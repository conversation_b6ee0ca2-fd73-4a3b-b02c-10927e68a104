module.exports = {
  collectionName: 'disk-file-log',
  info: {
    name: 'disk-file-log',
    label: '文件日志',
    description: '文件日志'
  },
  options: {
    timestamps: true,
    indexes: [
      // { keys: { key: 1 }, options: { unique: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      type: 'string',
      required: true,
    },
    period: {
      label: '学段',
      type: 'string',
      required: true,
    },
    subject: {
      label: '科目',
      type: 'string',
      required: true,
    },
    file: {
      label: '文件',
      model: 'disk-file',
    },
    operation_type: {
      label: '操作类型',
      type: 'string',
      required: true,
    },
    creator: {
      label: '创建人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'user'
    }
  }
}
