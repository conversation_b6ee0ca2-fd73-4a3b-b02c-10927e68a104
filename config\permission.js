const { createDefaultPermissions } = require('accel-utils')

// app
const apps = []
// 基础视图配置
const pageGroups = [
  {
    sId: 'PlatformOps',
    name: '平台运维🦸‍♂️',
    pages: [
      {
        sId: 'AccessKeyManagement', name: '访问密钥管理', icon: 'vpn_key',
        meta: {
          modelId: 'access-key',
          modelPath: 'access-keys',
        }
      },
    ],
  },
  {
    sId: 'KnowledgeBaseGroup',
    name: '知识体系',
    pages: [
      // {
      //   sId: 'PeriodManagement', name: '学段管理', icon: 'article',
      //   meta: {
      //     modelId: 'period',
      //     modelPath: 'periods',
      //   }
      // },
      // {
      //   sId: 'SubjectManagement', name: '科目管理', icon: 'article',
      //   meta: {
      //     modelId: 'subject',
      //     modelPath: 'subjects',
      //   }
      // },
      // {
      //   sId: 'PressVersionManagement', name: '教材版本管理', icon: 'article',
      //   meta: {
      //     modelId: 'press-version',
      //     modelPath: 'press-versions',
      //   }
      // },
      // {
      //   sId: 'BookManagement', name: '教材树管理', icon: 'article',
      //   meta: {
      //     modelId: 'book',
      //     modelPath: 'books',
      //   }
      // },
      // {
      //   sId: 'BookChapterManagement', name: '教材树章节管理', icon: 'article',
      //   meta: {
      //     modelId: 'book-chapter',
      //     modelPath: 'book-chapters',
      //   }
      // },
      {
        sId: 'TreeCatalogManagement', name: '学校树目录管理', icon: 'article',
        meta: {
          modelId: 'tree-catalog',
          modelPath: 'tree-catalogs',
        }
      },
      {
        sId: 'KnowledgeTreeManagement', name: '知识树管理', icon: 'article',
        meta: {
          modelId: 'knowledge-tree',
          modelPath: 'knowledge-trees',
        }
      },
      {
        sId: 'KnowledgeTreeChapterManagement', name: '知识树章节管理', icon: 'article',
        meta: {
          modelId: 'knowledge-tree-chapter',
          modelPath: 'knowledge-tree-chapters',
        }
      },
      {
        sId: 'KnowledgeManagement', name: '知识点管理', icon: 'article',
        meta: {
          modelId: 'knowledge',
          modelPath: 'knowledges',
        }
      },
    ],
  },
  {

    sId: 'ResourceGroup',
    name: '资源管理',
    pages: [
      {
        sId: 'BasketManagement', name: '试题篮管理', icon: 'article',
        meta: {
          modelId: 'basket',
          modelPath: 'baskets',
        }
      },
      {
        sId: 'ExampaperQuestionTemplateManagement', name: '试卷试题模板管理', icon: 'article',
        meta: {
          modelId: 'exampaper-question-template',
          modelPath: 'exampaper-question-templates',
        }
      },
      {
        sId: 'ExampaperManagement', name: '组卷管理', icon: 'article',
        meta: {
          modelId: 'exampaper',
          modelPath: 'exampapers',
        }
      },
      {
        sId: 'TwSpecificationManagement', name: '细目表管理', icon: 'article',
        meta: {
          modelId: 'tw-specification',
          modelPath: 'tw-specifications',
        }
      },
      {
        sId: 'ResourceManagement', name: '备课资源管理', icon: 'article',
        meta: {
          modelId: 'resource',
          modelPath: 'resources',
        }
      },
      {
        sId: 'QuestionManagement', name: '试题管理', icon: 'article',
        meta: {
          modelId: 'question',
          modelPath: 'questions',
        }
      },
      {
        sId: 'DownloadRecordManagement', name: '下载记录管理', icon: 'article',
        meta: {
          modelId: 'download-record',
          modelPath: 'download-records',
        }
       },
    ],
  },
  {
    sId: 'DiskGroup',
    name: '网盘管理',
    pages: [
      {
        sId: 'DiskFileManagement', name: '网盘文件管理', icon: 'article',
        meta: {
          modelId: 'disk-file',
          modelPath: 'disk-files',
        }
      },
      {
        sId: 'DiskFileLogManagement', name: '网盘文件日志管理', icon: 'article',
        meta: {
          modelId: 'disk-file-log',
          modelPath: 'disk-file-logs',
        }
      },
      {
        sId: 'ParseTaskManagement', name: '解析任务管理', icon: 'article',
        meta: {
          modelId: 'parse-task',
          modelPath: 'parse-tasks',
        }
      },
    ],
  },
]
// 基础功能配置
const functions = [
  {
    name: '知识体系',
    sId: 'KnowledgeBaseManagementFunction',
    pages: [
      // 'PeriodManagement',
      // 'SubjectManagement',
      // 'PressVersionManagement',
      // 'BookManagement',
      // 'BookChapterManagement',
      'TreeCatalogManagement',
      'KnowledgeTreeManagement',
      'KnowledgeTreeChapterManagement',
      'KnowledgeManagement',
    ],
    apiPermissions: [
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'period', }),
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'subject', }),
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'press-version', }),
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'book', }),
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'book-chapter', }),
      ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'tree-catalog', }),
      ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'knowledge-tree', }),
      ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'knowledge-tree-chapter', }),
      ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'knowledge', }),
    ]
  },
  {
    name: '资源管理',
    sId: 'ResourceManagementFunction',
    pages: [
      'BasketManagement',
      'ExampaperQuestionTemplateManagement',
      'ExampaperManagement',
      'TwSpecificationManagement',
      'ResourceManagement',
      'QuestionManagement',
      'DownloadRecordManagement',
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'basket', }),
      ...createDefaultPermissions({ type: 'application', controller: 'exampaper-question-template', }),
      ...createDefaultPermissions({ type: 'application', controller: 'exampaper', }),
      ...createDefaultPermissions({ type: 'application', controller: 'tw-specification', }),
      ...createDefaultPermissions({ type: 'application', controller: 'resource', }),
      ...createDefaultPermissions({ type: 'application', controller: 'question', }),
       ...createDefaultPermissions({ type: 'application', controller: 'download-record', }),
    ]
  },
  {
    name: '网盘管理',
    sId: 'DiskManagementFunction',
    pages: [
      'DiskFileManagement',
      'DiskFileLogManagement',
      'ParseTaskManagement',
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'disk-file', }),
      ...createDefaultPermissions({ type:'application', controller: 'disk-file-log', }),
       ...createDefaultPermissions({ type: 'application', controller: 'parse-task', }),
    ]
  },
  {
    name: '已登录',
    sId: 'AuthenticatedFunction',
    pages: [],
    apiPermissions: [
      // 试题篮
      { 'type': 'application', 'controller': 'basket', 'action': 'getQuestions' },
      { 'type': 'application', 'controller': 'basket', 'action': 'addQuestions' },
      { 'type': 'application', 'controller': 'basket', 'action': 'deleteQuestions' },
      { 'type': 'application', 'controller': 'basket', 'action': 'getBasket' },
      { 'type': 'application', 'controller': 'basket', 'action': 'deleteBasket' },
      // 细目表
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'getSysList' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'getUserList' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'getTableDetail' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'postKnowledge' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'createTable' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'updateTable' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'deleteTable' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'downloadTable' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'getTableByExampaper' },
      { 'type': 'application', 'controller': 'tw-specification', 'action': 'createPaper' },
      // 试卷试题模板
      { 'type': 'application', 'controller': 'exampaper-question-template', 'action': 'getUserList' },
      { 'type': 'application', 'controller': 'exampaper-question-template', 'action': 'createTemplate' },
      { 'type': 'application', 'controller': 'exampaper-question-template', 'action': 'deleteTemplateById' },
      // 试卷
      { 'type': 'application', 'controller': 'exampaper', 'action': 'knowledgePaper' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'paperToPaper' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'savePaper' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'getDetail' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'download' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'getDtkGateway' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'deletePaper' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'getExampaperList' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'search' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'addByUpload' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'getAllRef' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'addByUpload' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'updateShareStatus' },
      { 'type': 'application', 'controller': 'exampaper', 'action': 'refToPerson' },

      // 学科空间-备课资源
      { 'type': 'application', 'controller': 'resource', 'action': 'getList' },
      { 'type': 'application', 'controller': 'resource', 'action': 'getUserList' },
      { 'type': 'application', 'controller': 'resource', 'action': 'createResource' },
      { 'type': 'application', 'controller': 'resource', 'action': 'updateShareStatus' },
      { 'type': 'application', 'controller': 'resource', 'action': 'refToPerson' },
      { 'type': 'application', 'controller': 'resource', 'action': 'updateTimes' },
      { 'type': 'application', 'controller': 'resource', 'action': 'deleteById' },
      { 'type': 'application', 'controller': 'resource', 'action': 'getAllRef' },
      { 'type': 'application', 'controller': 'resource', 'action': 'updateChapter' },
      { 'type': 'application', 'controller': 'resource', 'action': 'getById' },
      // 下载记录
      { 'type': 'application', 'controller': 'download-record', 'action': 'getList' },
      { 'type': 'application', 'controller': 'download-record', 'action': 'deleteById' },

      ...createDefaultPermissions({ type: 'application', controller: 'exampaper', }),
      ...createDefaultPermissions({ type: 'application', controller: 'resource', }),
      ...createDefaultPermissions({ type: 'application', controller: 'download-record', }),

      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'period', }),
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'subject', }),
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'press-version', }),
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'book', }),
      // ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'book-chapter', }),
      ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'tree-catalog', }),
      ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'knowledge-tree', }),
      ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'knowledge-tree-chapter', }),
      ...createDefaultPermissions({ type: 'application', mode: 'branch', controller: 'knowledge', }),
      // { 'type': 'application', 'controller': 'period', 'action': 'getBookCatalogByBranch' },
      // { 'type': 'application', 'controller': 'period', 'action': 'syncBookCatalogByBranch' },
      // { 'type': 'application', 'controller': 'period', 'action': 'getPeriodCatalogByBranch' },
      // { 'type': 'application', 'controller': 'press-version', 'action': 'getPressVersionCatalogByBranch' },
      // { 'type': 'application', 'controller': 'press-version', 'action': 'syncPressVersionCatalogByBranch' },
      // { 'type': 'application', 'controller': 'book', 'action': 'getBookById' },
      // { 'type': 'application', 'controller': 'book', 'action': 'syncBookById' },

      { 'type': 'application', 'controller': 'tree-catalog', 'action': 'getPeriodCatalog' },
      { 'type': 'application', 'controller': 'tree-catalog', 'action': 'getBookTreeCatalog' },
      { 'type': 'application', 'controller': 'tree-catalog', 'action': 'updatePeriodCatalog' },
      { 'type': 'application', 'controller': 'tree-catalog', 'action': 'updateBookTreeCatalog' },

      { 'type': 'application', 'controller': 'knowledge-tree', 'action': 'getKnowledgeTreeByPeriodSubject' },
      { 'type': 'application', 'controller': 'knowledge-tree', 'action': 'syncKnowledgeTreeById' },
      { 'type': 'application', 'controller': 'knowledge', 'action': 'getKnowledgeListBySearch' },
      { 'type': 'application', 'controller': 'knowledge', 'action': 'addKnowledgeByChapter' },

      { 'type': 'users-permissions', 'controller': 'user', 'action': 'update' },

      // 通用
      { 'type': 'application', 'controller': 'study', 'action': 'getFilters' },
      { 'type': 'application', 'controller': 'study', 'action': 'getInfo' },
      { 'type': 'application', 'controller': 'study', 'action': 'updateInfo' },
      // 试题
      { 'type': 'application', 'controller': 'question', 'action': 'search' },
      // 用户
      { 'type': 'application', 'controller': 'user', 'action': 'getInfo' },
      // 网盘
      ...createDefaultPermissions({ type: 'application', controller: 'disk-file' }),
      { 'type': 'application', 'controller': 'disk-file', 'action': 'search' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'getFolder' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'getDetails' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'addFile' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'batchAddFile' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'updateFile' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'deleteFile' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'updateTopStatus' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'batchShare' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'cancelShare' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'updateFolder' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'download' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'getShareUsers' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'searchShareUserFiles' },
      { 'type': 'application', 'controller': 'disk-file', 'action': 'ref' },
      // 网盘日志
      ...createDefaultPermissions({ type: 'application', controller: 'disk-file-log' }),
      { 'type': 'application', 'controller': 'disk-file-log', 'action': 'getList' },
      { 'type': 'application', 'controller': 'disk-file-log', 'action': 'deleteById' },
      // 解析任务
      ...createDefaultPermissions({ type: 'application', controller: 'parse-task' }),
      { 'type': 'application', 'controller': 'parse-task', 'action': 'createTask' },
    ]
  },
  {
    name: '未登录',
    sId: 'PublicFunction',
    apiPermissions: [
      // 未登录用户允许查看文章和作者信息
      { 'type': 'application', 'controller': 'auth', 'action': 'loginByUnifyToken' },
    ]
  },
  {
    name: '访问密钥管理权限',
    sId: 'AccessKeyManagementFunction',
    pages: [
      'AccessKeyManagement'
    ],
    apiPermissions: [
      ...createDefaultPermissions({
        type: 'application', controller: 'access-key',
      })
    ]
  },
]

const roles = [
  {
    name: '管理员',
    type: 'admin',
    description: '平台管理员',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
    ]
  },
  {
    name: '阅卷老师',
    type: 'teacher',
    description: '阅卷老师',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
    ]
  },
]

module.exports = {
  pageGroups,
  functions,
  roles,
  apps
}
