
// 试题篮试题上限
const BasketQuestionLimit = 60;

const ExamPaperOtherType = '其他';

const PaperSourceType = {
  SYS: 'sys', // 系统
  ASSEMBLE: 'assemble', // 个人组卷
  EXAM: 'exam', // 考后巩固
  UPLOAD: 'upload', // 上传
  REF: 'ref', // 上传
}

const QuestionSource = {
  SYS: 'sys',
  UPLOAD: 'upload', // 上传
  // REF: 'ref', // 引用
  JY: 'jy', // 校验平台
}

// 试卷模板类型
const PaperTemplateType = {
  SYS: 'sys',
  CUSTOM: 'custom'
};

const QuestionDifficultyNumber = {
  VERY_EASY: 1,
  EASY: 2,
  NORMAL: 3,
  HARD: 4,
  VERY_HARD: 5
};

const QuestionDifficultyName = {
  [QuestionDifficultyNumber.VERY_EASY]: '容易',
  [QuestionDifficultyNumber.EASY]: '较易',
  [QuestionDifficultyNumber.NORMAL]: '中等',
  [QuestionDifficultyNumber.HARD]: '较难',
  [QuestionDifficultyNumber.VERY_HARD]: '困难',
}

const Bool = {
  NO: 0,
  YES: 1
}

const ResourceFrom = {
  upload: 'upload',
  REF: 'ref',
}

const AdminRoles = ['超级管理员', '年级主任', '校领导'];

const DownloadResource = {
  SYS_EXAMPAPER: 'sys_exampaper', // 系统试卷
  EXAMPAPER: 'exampaper', // 试卷
  RESOURCE: 'resource', // 备课资源
}

// 知识点同步方式
const KnowledgeSyncType = {
  MANUAL: 'manual',
  SYNC: 'sync'
}
// 试卷状态
const PaperStatus = {
  INIT: 'init',
  EDIT: 'edit',
  DONE: 'done',
  ERROR: 'error',
}
// 空间
const Space = {
  SCHOOL: 'school',
  PERSON: 'person',
}

const ActionType = {
  VIEW_TIMES: 'view_times',
  DOWNLOAD_TIMES: 'download_times',

}

const CollectionName = {
  BRANCH_PROPERTY: 'branch-property',
  DISK_FILE: 'disk-file', // 网盘文件
  DISK_FILE_LOG: 'disk-file-log', // 网盘文件日志
  PARSE_TASK: 'parse-task', // 解析任务
  TREE_CATALOG: 'tree-catalog', // 树目录
}

const BranchPropertyKey = {
  PERIOD_SUBJECT: 'period_subject'
}

const FileSource = {
  UPLOAD: 'upload', // 上传
  REF: 'ref', // 转存
}

const FileCategory = {
  DOCUMENT: 'document', // 文档
  IMAGE: 'image', // 图片
  VIDEO: 'video', // 视频
  AUDIO: 'audio', // 音频
  OTHER: 'other', // 其他
}
// 文件操作类型
const FileOperationType = {
  UPLOAD: 'upload',
  DOWNLOAD: 'download'
}

const FileLogSource = {
  USER: 'user',
  SHARE: 'share'
}

const YjGradeMapping = [
  { yj_grade: '一年级', grade: '一年级', period: '小学'},
  { yj_grade: '二年级', grade: '二年级', period: '小学'},
  { yj_grade: '三年级', grade: '三年级', period: '小学'},
  { yj_grade: '四年级', grade: '四年级', period: '小学'},
  { yj_grade: '五年级', grade: '五年级', period: '小学'},
  { yj_grade: '六年级', grade: '六年级', period: '小学'},
  { yj_grade: '初一', grade: '七年级', period: '初中'},
  { yj_grade: '初二', grade: '八年级', period: '初中'},
  { yj_grade: '初三', grade: '九年级', period: '初中'},
  { yj_grade: '初四', grade: '九年级', period: '初中'},
  { yj_grade: '直升初一', grade: '七年级', period: '初中'},
  { yj_grade: '直升初二', grade: '八年级', period: '初中'},
  { yj_grade: '七年制初一', grade: '七年级', period: '初中'},
  { yj_grade: '七年制初二', grade: '八年级', period: '初中'},
  { yj_grade: '七年制初三', grade: '九年级', period: '初中'},
  { yj_grade: '二四制初一', grade: '七年级', period: '初中'},
  { yj_grade: '二四制初二', grade: '八年级', period: '初中'},
  { yj_grade: '高一', grade: '高一', period: '高中'},
  { yj_grade: '高二', grade: '高二', period: '高中'},
  { yj_grade: '高三', grade: '高三', period: '高中'},
  { yj_grade: '直升高一', grade: '高一', period: '高中'},
  { yj_grade: '四年制高一', grade: '高一', period: '高中'},
  { yj_grade: '四年制高二', grade: '高二', period: '高中'},
  { yj_grade: '四年制高三', grade: '高三', period: '高中'},
  { yj_grade: '四年制高四', grade: '高三', period: '高中'},
  { yj_grade: '国际部高一', grade: '高一', period: '高中'},
  { yj_grade: '国际部高二', grade: '高二', period: '高中'},
  { yj_grade: '国际部高三', grade: '高三', period: '高中'},
  { yj_grade: '国际部高四', grade: '高三', period: '高中'},
];

const SubjectRules = {
  '语文': ['语文'],
  '数学': ['数学', '数'],
  '英语': ['英语', '英'],
  '物理': ['物理'],
  '化学': ['化学'],
  '生物': ['生物'],
  '地理': ['地理'],
  '历史': [ '历', '史'],
  '政治': ['政', '思想品德', '思品', '道法', '道德与法治', '道德法治', '道德与法治 / 政治'],
  '道德与法治': ['政', '思想品德', '思品', '道法', '道德与法治', '道德法治', '道德与法治 / 政治']
};



module.exports = {
  BasketQuestionLimit,
  ExamPaperOtherType,
  PaperSourceType,
  QuestionSource,
  PaperTemplateType,
  QuestionDifficultyNumber,
  QuestionDifficultyName,
  Bool,
  ResourceFrom,
  AdminRoles,
  DownloadResource,
  KnowledgeSyncType,
  PaperStatus,
  Space,
  ActionType,
  CollectionName,
  BranchPropertyKey,
  FileSource,
  FileCategory,
  FileOperationType,
  FileLogSource,
  YjGradeMapping,
  SubjectRules,
}
