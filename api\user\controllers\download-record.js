const { CurdRouter } = require('accel-utils');
const _ = require('lodash');
const moment = require('moment');
const enums = require('../../common/enums');
const MODEL = 'download-record';
const curdRouter = new CurdRouter(MODEL);


module.exports = {
  ...curdRouter.createHandlers(),
  getList,
  deleteById
}
// 获取用户下载记录
async function getList(ctx) {
  const user = ctx.state.user;
  const {period, subject, time = 0, offset = 0, limit = 10} = ctx.request.query;
  const query = {
    period,
    subject,
    createdAt_gte: moment(Number(time)).startOf('day').toDate(),
    user: user.id,
    deleted: enums.Bool.NO,
    _start: offset,
    _limit: limit,
    _sort: 'createdAt:DESC'
  }
  const result = {
    total: 0,
    list: []
  };
  result.total = await strapi.query(MODEL).count(query);
  if (!result.total) return ctx.wrapper.succ(result);
  result.list = await strapi.query(MODEL).find(query);
  for (const data of result.list) {
    data.period = _.pick(data.period, ['id', 'name']);
    data.subject = _.pick(data.subject, ['id', 'name']);
    delete data.user;
    delete data.pBranch;
  }
  return ctx.wrapper.succ(result);
}

// 删除下载记录
async function deleteById(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  const data = await strapi.query(MODEL).findOne({id, user: user.id});
  if (_.isEmpty(data)) return ctx.wrapper.error('HANDLE_ERROR', '删除数据不存在');
  await strapi.query(MODEL).update({id, user: user.id }, {deleted: enums.Bool.YES});
  return ctx.wrapper.succ({id});
}
