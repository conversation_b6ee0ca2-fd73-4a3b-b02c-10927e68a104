module.exports = {
  collectionName: 'resource',
  info: {
    name: 'resource',
    label: '备课资源',
    description: '备课资源'
  },
  options: {
    timestamps: true,
    // indexes: [
    //   { keys: { key: 1 }, options: { unique: true } },
    // ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名字',
      type: 'string',
      required: true,
    },
    type: {
      label: '类型',
      type: 'string',
      required: true,
      options: [
        {
          label: '课件',
          value: 'lesson_ware'
        },
        {
          label: '教案',
          value: 'teach_plan'
        },
        {
          label: '学案',
          value: 'study_plan'
        },
        {
          label: '同步作业',
          value: 'homework'
        },
        {
          label: '试卷',
          value: 'paper'
        },
        {
          label: '视频',
          value: 'video'
        },
        {
          label: '音频',
          value: 'audio'
        },
        {
          label: '其他',
          value: 'other'
        }
      ]
    },
    period: {
      label: '学段',
      model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      model: 'subject',
      required: true,
    },
    press_version: {
      label: '教材版本',
      model: 'press-version',
      required: true,
    },
    book: {
      label: '学科',
      model: 'book',
      required: true,
    },
    book_chapter: {
      label: '章节',
      model: 'book-chapter',
      required: true,
    },
    url: {
      label: '文件地址',
      type: 'string',
      required: true,
    },
    size: {
      label: '文件大小',
      type: 'integer',
      required: true,
    },
    suffix: {
      label: '后缀',
      type: 'string',
      required: true,
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'upload',
      options: [
        {
          label: '上传',
          value: 'upload'
        },
        {
          label: '引用',
          value: 'ref'
        }
      ]
    },
    source_id: {
      label: '来源ID',
      type: 'string',
      default: 'upload',
      required: true
    },
    download_times: {
      label: '下载次数',
      type: 'number',
      default: 0,
      required: true
    },
    last_download_time: {
      label: '最近下载时间',
      type: 'datetime',
    },
    view_times: {
      label: '浏览次数',
      type: 'number',
      default: 0,
      required: true
    },
    last_view_time: {
      label: '最后浏览时间',
      type: 'datetime',
    },
    shared: {
      label: '分享标识',
      type: 'number',
      default: 0,
    },
    user: {
      label: '用户',
      plugin: 'users-permissions',
      model: 'user',
      configurable: false
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0,
    }
  }
}
