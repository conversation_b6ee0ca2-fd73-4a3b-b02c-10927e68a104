module.exports = {
  load: {
    before: [
      'responseWrapper',
      'koaBody',
      'responseTime',
      'logger',
      'cors',
      'responses',
      'gzip',
    ],
    order: [
      'originFilter',
    ],
    after: [
      'parser',
      'router',
    ],
  },
  settings: {
    cors: {
      origin (ctx) {
        const baseOrigin = []
        const requestOrigin = ctx.accept.headers.origin
        const testOrigin = /(127\.0\.0\.1|localhost|local)/.test(
          requestOrigin)
          ? [ctx.accept.headers.origin]
          : []
        const externalOrigin = /(yunxiao\.com|yuejuanjia\.com|jianyu360\.cn|yxzhixue\.com)/.test(requestOrigin)
          ? [ctx.accept.headers.origin]
          : []
        return [
          ...baseOrigin,
          ...testOrigin,
          ...externalOrigin,
        ]
      },
      headers: [
        'Content-Type',
        'Authorization',
        'Origin',
        'Accept',
        'Cache-Control',
        'X-Space',
        'X-From'],
    },
    responseWrapper: {
      enabled: true,
    },
    spaceFilter: {
      enabled: true,
    },
    originFilter: {
      enabled: true,
    },
    parser: {
      formLimit: '1024mb',
      // strict: true,
      parsedMethods: ['POST', 'PUT', 'PATCH', 'DELETE'],
      formidable: {
        maxFileSize: 1024 * 1024 * 1024, // multipart data, modify here limit of uploaded file size
      },
    },
  },
}
