module.exports = {
  collectionName: 'tw-specification',
  info: {
    name: 'tw-specification',
    label: '细目表',
    description: '用户细目表'
  },
  options: {
    timestamps: true,
    // indexes: [
    //   { keys: { key: 1 }, options: { unique: true } },
    // ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      type: 'string',
      required: true,
    },
    period: {
      label: '学段',
      model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      model: 'subject',
      required: true,
    },
    grade: {
      label: '年级',
      type: 'string',
    },
    type: {
      label: '类型',
      type: 'string',
    },
    province: {
      label: '省份',
      type: 'string',
      default: ''
    },
    city: {
      label: '城市',
      type: 'string',
      default: ''
    },
    table_id: {
      label: 'KB细目表ID',
      type: 'string',
      required: true
    },
    user: {
      label: '用户',
      plugin: 'users-permissions',
      model: 'user',
      configurable: false
    },
  }
}
