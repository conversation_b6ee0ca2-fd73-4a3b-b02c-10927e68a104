module.exports = {
  collectionName: 'knowledge-tree-chapter',
  info: {
    name: 'knowledge-tree-chapter',
    label: '知识树章节',
    description: '知识树章节'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { key: 1 }, options: { unique: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '教材章节名称',
      type: 'string',
      required: true,
    },
    path: {
      label: '路径', //格式：学段id-学科id-知识树id-一级知识点id-二级知识点id-本级知识点id
      type: 'string',
      required: true,
    },
    knowledges: {
      label: '知识点',
      collection: 'knowledge',
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'manual',
      options: [
        {
          label: '同步',
          value: 'sync'
        },
        {
          label: '手动',
          value: 'manual'
        }
      ]
    },
    source_id: {
      label: '来源id',
      type: 'number',
    },
    key: {
      label: 'key', // 学段id-学科id-知识树id-一级知识点id-二级知识点id-name
      required: true,
      type: 'string',
    },
    period: {
      label: '学段',
      type: 'string',
      // model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      type: 'string',
      // model: 'subject',
      required: true,
    },
    knowledge_tree: {
      label: '知识树',
      model: 'knowledge-tree',
      required: true,
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
