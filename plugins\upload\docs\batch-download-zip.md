# 批量下载文件并打包成ZIP功能

这个功能允许你通过提供多个对象存储URL，批量下载文件并将它们打包成一个ZIP文件返回。

## 功能特点

- ✅ 支持批量下载多个文件
- ✅ 自动打包成ZIP格式
- ✅ 支持自定义文件名
- ✅ 并发下载优化（限制并发数为5）
- ✅ 错误处理和重试机制
- ✅ 下载结果摘要
- ✅ 文件名安全处理
- ✅ 超时控制
- ✅ 流式响应，内存友好

## API接口

### 请求

```
POST /upload/external/batch-download-zip
Content-Type: application/json
```

### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| urls | Array | 是 | - | 要下载的URL数组 |
| zipName | String | 否 | files.zip | ZIP文件名 |
| timeout | Number | 否 | 30000 | 单个文件下载超时时间（毫秒） |

#### URLs格式

支持两种格式：

1. **字符串数组**：
```json
{
  "urls": [
    "https://example.com/file1.pdf",
    "https://example.com/file2.jpg",
    "https://example.com/file3.docx"
  ]
}
```

2. **对象数组**（支持自定义文件名）：
```json
{
  "urls": [
    {
      "url": "https://example.com/document.pdf",
      "filename": "重要文档.pdf"
    },
    {
      "url": "https://example.com/image.jpg",
      "filename": "图片1.jpg"
    },
    "https://example.com/simple-url.txt"
  ]
}
```

### 响应

成功时返回ZIP文件流，响应头包含：
```
Content-Type: application/zip
Content-Disposition: attachment; filename="files.zip"
```

失败时返回JSON错误信息：
```json
{
  "code": "HANDLE_ERROR",
  "message": "错误描述"
}
```

## 使用限制

- 最多支持100个文件
- 单个文件默认超时30秒
- 并发下载限制为5个文件
- 文件名长度限制200字符

## ZIP文件内容

下载的ZIP文件包含：

1. **成功下载的文件**：使用原始文件名或自定义文件名
2. **download_summary.json**：下载结果摘要文件

### download_summary.json格式

```json
{
  "downloadTime": "2024-01-01T12:00:00.000Z",
  "totalFiles": 5,
  "successCount": 4,
  "failedCount": 1,
  "successFiles": [
    {
      "url": "https://example.com/file1.pdf",
      "filename": "file1.pdf",
      "size": 1024000
    }
  ],
  "failedFiles": [
    {
      "url": "https://example.com/invalid.jpg",
      "error": "下载文件失败: 404 Not Found"
    }
  ]
}
```

## 使用示例

### Node.js示例

```javascript
const axios = require('axios')
const fs = require('fs')

async function downloadFiles() {
  try {
    const response = await axios.post('http://localhost:1337/upload/external/batch-download-zip', {
      urls: [
        'https://example.com/file1.pdf',
        'https://example.com/file2.jpg'
      ],
      zipName: 'my-files.zip'
    }, {
      responseType: 'stream'
    })
    
    const writer = fs.createWriteStream('./downloaded-files.zip')
    response.data.pipe(writer)
    
    writer.on('finish', () => {
      console.log('下载完成!')
    })
  } catch (error) {
    console.error('下载失败:', error.message)
  }
}

downloadFiles()
```

### 浏览器示例

```javascript
async function downloadFiles() {
  try {
    const response = await fetch('/upload/external/batch-download-zip', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        urls: [
          'https://example.com/file1.pdf',
          'https://example.com/file2.jpg'
        ],
        zipName: 'browser-download.zip'
      })
    })
    
    if (!response.ok) {
      throw new Error('下载失败')
    }
    
    const blob = await response.blob()
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'browser-download.zip'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载失败:', error)
  }
}
```

### cURL示例

```bash
curl -X POST \
  http://localhost:1337/upload/external/batch-download-zip \
  -H 'Content-Type: application/json' \
  -d '{
    "urls": [
      "https://example.com/file1.pdf",
      "https://example.com/file2.jpg"
    ],
    "zipName": "curl-download.zip"
  }' \
  --output downloaded-files.zip
```

## 错误处理

该功能具有良好的错误处理机制：

1. **部分失败**：即使某些文件下载失败，成功的文件仍会被打包
2. **详细日志**：download_summary.json包含详细的成功/失败信息
3. **超时处理**：单个文件超时不会影响其他文件
4. **网络错误**：自动处理网络连接问题

## 性能优化

- 使用流式处理，避免大文件占用过多内存
- 限制并发数量，避免过多连接
- 支持超时控制，避免长时间等待
- ZIP压缩级别优化

## 安全考虑

- 文件名安全处理，移除危险字符
- URL验证，防止恶意请求
- 文件大小限制（通过超时间接控制）
- 并发限制，防止资源滥用

## 故障排除

### 常见问题

1. **所有文件都下载失败**
   - 检查URL是否可访问
   - 检查网络连接
   - 增加超时时间

2. **部分文件下载失败**
   - 查看download_summary.json了解具体错误
   - 检查失败文件的URL

3. **下载速度慢**
   - 检查网络带宽
   - 考虑减少并发文件数量
   - 增加超时时间

4. **ZIP文件损坏**
   - 检查服务器日志
   - 确认所有文件下载完成
   - 重新尝试下载
