'use strict'

const { CurdRouter } = require('accel-utils')
const _ = require('lodash')
const Joi = require('joi')
const enums = require('../../common/enums')

const curdRouter = new CurdRouter({
  collectionName: enums.CollectionName.SCHOOL_TEXTBOOK,
  defaultSort: { sort_order: -1, createdAt: -1 },
  defaultPopulate: ['creator', 'operator', 'cover_file', 'disk_files'],
  searchFields: ['name', 'description', 'tags'],
  filterFields: ['grade', 'period', 'subject', 'category', 'status', 'creator', 'is_featured', 'is_top'],
})

// 创建校本教辅的参数验证
const JOI_CREATE_TEXTBOOK = Joi.object({
  name: Joi.string().required().max(200),
  description: Joi.string().allow('').default(''),
  grade: Joi.string().required(),
  period: Joi.string().required(),
  subject: Joi.string().required(),
  cover_url: Joi.string().allow('').default(''),
  cover_file: Joi.string().length(24).allow('').optional(),
  disk_files: Joi.array().items(Joi.string().length(24)).default([]),
  category: Joi.string().default('练习册'),
  tags: Joi.array().items(Joi.string()).default([]),
  status: Joi.string().valid('draft', 'published', 'archived').default('draft'),
  is_featured: Joi.boolean().default(false),
  is_top: Joi.boolean().default(false),
  sort_order: Joi.number().default(0),
  remark: Joi.string().allow('').default(''),
})

// 更新校本教辅的参数验证
const JOI_UPDATE_TEXTBOOK = Joi.object({
  name: Joi.string().max(200).optional(),
  description: Joi.string().allow('').optional(),
  grade: Joi.string().optional(),
  period: Joi.string().optional(),
  subject: Joi.string().optional(),
  cover_url: Joi.string().allow('').optional(),
  cover_file: Joi.string().length(24).allow('').optional(),
  disk_files: Joi.array().items(Joi.string().length(24)).optional(),
  category: Joi.string().optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  status: Joi.string().valid('draft', 'published', 'archived').optional(),
  is_featured: Joi.boolean().optional(),
  is_top: Joi.boolean().optional(),
  sort_order: Joi.number().optional(),
  remark: Joi.string().allow('').optional(),
})

// 批量操作参数验证
const JOI_BATCH_OPERATION = Joi.object({
  ids: Joi.array().items(Joi.string().length(24)).required().min(1),
  operation: Joi.string().valid('publish', 'archive', 'delete', 'feature', 'unfeature', 'top', 'untop').required(),
})

/**
 * 创建校本教辅
 */
async function createTextbook(ctx) {
  const { error, value } = JOI_CREATE_TEXTBOOK.validate(ctx.request.body)
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)

  const user = ctx.state.user
  const textbookData = {
    ...value,
    creator: user.id,
    operator: user.id,
    pBranch: user.pBranch.id,
  }

  // 如果有关联文件，计算文件统计信息
  if (value.disk_files && value.disk_files.length > 0) {
    const files = await strapi.query(enums.CollectionName.DISK_FILE).find({
      id_in: value.disk_files,
      pBranch: user.pBranch.id,
      deleted: enums.Bool.NO
    })

    textbookData.file_count = files.length
    textbookData.total_size = files.reduce((sum, file) => sum + (file.size || 0), 0)

    // 更新文件的关联关系
    await Promise.all(files.map(file => 
      strapi.query(enums.CollectionName.DISK_FILE).update(
        { id: file.id },
        { school_textbook: null } // 先清空，创建后再关联
      )
    ))
  }

  // 设置发布时间
  if (textbookData.status === 'published') {
    textbookData.publish_time = new Date()
  }

  const textbook = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).create(textbookData)

  // 更新文件关联
  if (value.disk_files && value.disk_files.length > 0) {
    await Promise.all(value.disk_files.map(fileId => 
      strapi.query(enums.CollectionName.DISK_FILE).update(
        { id: fileId },
        { school_textbook: textbook.id }
      )
    ))
  }

  return ctx.wrapper.succ(textbook)
}

/**
 * 更新校本教辅
 */
async function updateTextbook(ctx) {
  const { error, value } = JOI_UPDATE_TEXTBOOK.validate(ctx.request.body)
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)

  const user = ctx.state.user
  const textbookId = ctx.params.id

  // 检查权限
  const existingTextbook = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).findOne({
    id: textbookId,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO
  })

  if (!existingTextbook) {
    return ctx.wrapper.error('HANDLE_ERROR', '校本教辅不存在或无权限访问')
  }

  const updateData = {
    ...value,
    operator: user.id,
  }

  // 如果更新了关联文件，重新计算统计信息
  if (value.disk_files !== undefined) {
    if (value.disk_files.length > 0) {
      const files = await strapi.query(enums.CollectionName.DISK_FILE).find({
        id_in: value.disk_files,
        pBranch: user.pBranch.id,
        deleted: enums.Bool.NO
      })

      updateData.file_count = files.length
      updateData.total_size = files.reduce((sum, file) => sum + (file.size || 0), 0)

      // 清空旧的关联关系
      await strapi.query(enums.CollectionName.DISK_FILE).update(
        { school_textbook: textbookId },
        { school_textbook: null }
      )

      // 建立新的关联关系
      await Promise.all(value.disk_files.map(fileId => 
        strapi.query(enums.CollectionName.DISK_FILE).update(
          { id: fileId },
          { school_textbook: textbookId }
        )
      ))
    } else {
      updateData.file_count = 0
      updateData.total_size = 0

      // 清空关联关系
      await strapi.query(enums.CollectionName.DISK_FILE).update(
        { school_textbook: textbookId },
        { school_textbook: null }
      )
    }
  }

  // 设置发布时间
  if (value.status === 'published' && existingTextbook.status !== 'published') {
    updateData.publish_time = new Date()
  }

  const textbook = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).update(
    { id: textbookId },
    updateData
  )

  return ctx.wrapper.succ(textbook)
}

/**
 * 批量操作校本教辅
 */
async function batchOperation(ctx) {
  const { error, value } = JOI_BATCH_OPERATION.validate(ctx.request.body)
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`)

  const user = ctx.state.user
  const { ids, operation } = value

  // 检查权限
  const textbooks = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).find({
    id_in: ids,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO
  })

  if (textbooks.length !== ids.length) {
    return ctx.wrapper.error('HANDLE_ERROR', '部分校本教辅不存在或无权限访问')
  }

  let updateData = { operator: user.id }

  switch (operation) {
    case 'publish':
      updateData.status = 'published'
      updateData.publish_time = new Date()
      break
    case 'archive':
      updateData.status = 'archived'
      break
    case 'delete':
      updateData.deleted = enums.Bool.YES
      break
    case 'feature':
      updateData.is_featured = true
      break
    case 'unfeature':
      updateData.is_featured = false
      break
    case 'top':
      updateData.is_top = true
      break
    case 'untop':
      updateData.is_top = false
      break
  }

  // 批量更新
  const results = await Promise.all(
    ids.map(id => 
      strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).update({ id }, updateData)
    )
  )

  return ctx.wrapper.succ({ 
    updated_count: results.length,
    operation: operation 
  })
}

/**
 * 增加浏览次数
 */
async function increaseViewTimes(ctx) {
  const textbookId = ctx.params.id
  const user = ctx.state.user

  const textbook = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).findOne({
    id: textbookId,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    status: 'published'
  })

  if (!textbook) {
    return ctx.wrapper.error('HANDLE_ERROR', '校本教辅不存在或未发布')
  }

  await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).update(
    { id: textbookId },
    { 
      view_times: (textbook.view_times || 0) + 1,
      operator: user.id
    }
  )

  return ctx.wrapper.succ({ view_times: (textbook.view_times || 0) + 1 })
}

/**
 * 增加下载次数
 */
async function increaseDownloadTimes(ctx) {
  const textbookId = ctx.params.id
  const user = ctx.state.user

  const textbook = await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).findOne({
    id: textbookId,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    status: 'published'
  })

  if (!textbook) {
    return ctx.wrapper.error('HANDLE_ERROR', '校本教辅不存在或未发布')
  }

  await strapi.query(enums.CollectionName.SCHOOL_TEXTBOOK).update(
    { id: textbookId },
    { 
      download_times: (textbook.download_times || 0) + 1,
      operator: user.id
    }
  )

  return ctx.wrapper.succ({ download_times: (textbook.download_times || 0) + 1 })
}

module.exports = {
  ...curdRouter.createHandlers(),
  createTextbook,
  updateTextbook,
  batchOperation,
  increaseViewTimes,
  increaseDownloadTimes,
}
