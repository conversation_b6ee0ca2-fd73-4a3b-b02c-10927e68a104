const { createDefaultRoutes } = require('accel-utils')
module.exports = {
  'routes': [
    {
      'method': 'GET',
      'path': '/study/actions/getFilters',
      'handler': 'study.getFilters',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/study/actions/getInfo',
      'handler': 'study.getInfo',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/study/actions/updateInfo',
      'handler': 'study.updateInfo',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
  ]
}
