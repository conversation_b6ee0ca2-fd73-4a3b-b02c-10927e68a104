/**
 * 细目表
 **/
const { CurdRouter } = require('accel-utils');
const _ = require('lodash');
const moment = require('moment');
const client = require('../../common/client');
const enums = require('../../common/enums');
const excel = require('../../common/lib/excel');
const studyService = require('../../common/services/study');
const basketService = require('../services/basket');
const MODEL = 'tw-specification';
const curdRouter = new CurdRouter(MODEL);

module.exports = {
  ...curdRouter.createHandlers(),
  getSysList,
  getUserList,
  getTableDetail,
  postKnowledge,
  createTable,
  updateTable,
  deleteTable,
  downloadTable,
  getTableByExampaper,
  createPaper,
}

/**
 * 获取系统细目表列表
 */
async function getSysList(ctx) {
  const query = ctx.request.query;
  // if (!query.sort_by) query.sort_by = 'year';
  const result = await client.kb.getHotTable(query);
  return ctx.wrapper.succ(result);
}

async function getPeriodSubjectById(period, subject) {
  const periodDoc = await strapi.query('period').findOne({id: period});
  const subjectDoc = await strapi.query('subject').findOne({id: subject});
  return {period_name: periodDoc.name, subject_name: subjectDoc.name};
}

async function getUserList(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.query;
  const {offset, limit, type, sort_by } = ctx.request.query;
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);

  const cond = {
    user: user.id,
    period: studyInfo.period,
    subject: studyInfo.subject,
  };
  if (type) cond.type = type;
  const result = {
    total: 0,
    list: []
  };
  let docs = await strapi.query(MODEL).find(cond);
  let tableIds = [];
  for (const doc of docs) {
    tableIds.push(doc.table_id);
  }
  if (!_.size(tableIds)) return ctx.wrapper.succ(result);
  const tables = await client.kb.getTableByIds(tableIds);
  //构建返回信息
  let resArr = [];
  for (let ele of tables) {
    let obj = {};
    obj.id = ele.id;
    obj.name = ele.name;
    obj.period = ele.period ? ele.period : '';
    obj.type = ele.type ? ele.type : '';
    obj.subject = ele.subject ? ele.subject : '';
    obj.ctime = ele.ctime ? ele.ctime : '';
    obj.view_times = ele.view_times ? ele.view_times : 0;
    obj.download_times = ele.download_times ? ele.download_times : 0;
    obj.province = ele.province ? ele.province : '';
    obj.grade = ele.grade ? ele.grade : '';
    if (ele.year)
      obj.year = Number(ele.year);
    else {
      if (ele.utime) {
        obj.year = moment(ele.utime).get('year');
      } else if (ele.ctime) {
        obj.year = moment(ele.ctime).get('year');
      } else {
        obj.year = '';
      }
    }
    if (!type)
      resArr.push(obj);
    else {
      if (type === obj.type)
        resArr.push(obj);
    }
  }

  // 按时间排序
  if (sort_by === 'time') {
    resArr.sort(function (x, y) {
      const tmpX = docs.find(e => e.table_id === x.id);
      const tmpY = docs.find(e => e.table_id === y.id);
      return moment(tmpY.createAt).valueOf() - moment(tmpX.createAt).valueOf();
      // Number(y.year) - Number(x.year);
    });
  }
  //按使用次数排序
  if (sort_by === 'use_times') {
    resArr.sort(function (x, y) {
      return y.view_times - x.view_times;
    });
  }

  let count = resArr.length;
  if (limit > 0)
    resArr = resArr.slice(offset, offset + limit);
  return ctx.wrapper.succ({
    total: count,
    list: resArr
  })
}
// // 获取细目表详细
async function getTableDetail(ctx) {
  const {table_id} = ctx.params;
  const result = await client.kb.getTableById(table_id);
  return ctx.wrapper.succ(result);
}
// // 知识点匹配
async function postKnowledge(ctx) {
  const result = await client.kb.getUnableKnowledge(ctx.request.body);
  return ctx.wrapper.succ(result);
}
// 创建细目表
async function createTable(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.body;
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  delete params.from;
  params.permission = 'private';
  params.relevance_type = null;
  params.relevance_id = null;
  // 调用kb接口添加细目表
  const new_table = await client.kb.createTable(params);
  if (_.isEmpty(new_table)) return ctx.wrapper.error('HANDLE_ERROR', '添加细目表失败');
  await strapi.query(MODEL).create({
    user: user.id,
    table_id: new_table.id,
    type: params.type,
    period: studyInfo.period,
    subject: studyInfo.subject,
    grade: params.grade || '',
    province: params.province || '',
    city: params.city || '',
  });
  return ctx.wrapper.succ({id: new_table.id});
}

// const JOI_UPDATE_TABLE = Joi.object({
//   table_id: Joi.string().required(),
//   name: Joi.string().required(),
//   period: Joi.string().required(),
//   subject: Joi.string().required(),
//   grade: Joi.string().required(),
//   type: Joi.string().required(),
//   province: Joi.string().optional(),
//   blocks: Joi.array().items(Joi.object()).required().min(1)
// });
// // 修改细目表
async function updateTable(ctx) {
  const user = ctx.state.user;
  const {table_id} = ctx.params;
  const params = ctx.request.body;
  const doc = await strapi.query(MODEL).findOne({table_id: table_id, user: user.id});
  if (_.isEmpty(doc)) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  // 调用kb修改细目表
  const updateParams = _.pick(params, ['name', 'period', 'subject', 'grade', 'type', 'province', 'blocks']);
  await client.kb.updateTable(table_id, updateParams);
  return ctx.wrapper.succ({id: table_id});
}
// // 删除细目表
async function deleteTable(ctx) {
  const user_id = ctx.state.user.id;
  const { table_id } = ctx.params;
  // 删除用
  const deleteDocs = await strapi.query(MODEL).delete({table_id, user: user_id});
  if (_.size(deleteDocs)) {
    await client.kb.deleteTableById(table_id);
  }
  return ctx.wrapper.succ({id: table_id});
}
// // 下载细目表
async function downloadTable(ctx) {
  const params = ctx.request.body;
  const {id} = params;
  if (id) { // 增加下载次数
    await client.kb.downloadTimesInc(id);
  }
  // 生成文件
  // const table = {};
  const buffer = generateExcel(params);
  ctx.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'); // Excel 文件的 MIME 类型
  ctx.set('Content-Disposition', `attachment; filename="example.xlsx`); // 告诉浏览器以附件形式下载文件
  // 创建文件流并赋值给 ctx.body
  // ctx.body = fs.createReadStream(filePath);
  ctx.body = buffer;
  // return
}
// // 查看试卷的细目表
async function getTableByExampaper(ctx) {
  const {id} = ctx.params;
  const {from} = ctx.request.query;

  let table = await client.kb.getTableByRefId(id, true);
  if (!_.isEmpty(table)) return ctx.wrapper.succ(table);
  const paper = await client.kb.getPaperById(id);
  const ids = [];
  for (const block of paper.blocks) {
    ids.push(...block.questions.map(e => e.id));
  }
  const questions = await client.kb.getQuestionByIds(ids);
  for (const block of paper.blocks) {
    for (const ques of block.questions) {
      const question = questions.find(e => e.id === ques.id);
      ques.knowledges = question.knowledges.map(k => {
        return {
          id: k.id,
          name: k.name
        }
      })
    }
  }
  table = _exampaper2table(paper);
  table.relevance_type = 'exampaper';
  table.relevance_id = id;
  table.permission = 'public';
  const new_table = await client.kb.createTable(table);
  table.id = new_table.id;
  return ctx.wrapper.succ(table);
}

function generateExcel(table) {
  return excel.getExcelFromTable(table, 'data/xlsx');
  // return {
  //   url: config.get('app.url') + '/download/xlsx/' + table.name + '.xlsx'
  // };
}

// const JOI_CREATE_PAPER = Joi.object({
//   id: Joi.string().optional(),
//   name: Joi.string().optional(),
//   period: Joi.string().required(),
//   subject: Joi.string().required(),
//   grade: Joi.string().required(),
//   type: Joi.string().required(),
//   province: Joi.string().optional(),
//   blocks: Joi.array().items(Joi.object()).required().min(1)
// });

async function createPaper(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.body;
  const {period, subject} = params;
  const algo_params = {
    period: params.period,
    subject: params.subject,
    grade: params.grade || '',
    type: '细目表组卷',
    // school_id: user && user.school_id,
    user_id: user.id,
    blocks: params.blocks,
    filtered_ques: []
  };
  _filter_table_empty_knowledges(algo_params);
  _handler_question_diff(algo_params);
  const algo_paper = await client.algo.detailTablePaper(algo_params);
  if (_.isEmpty(algo_paper) || !_.size(algo_paper.blocks)) return ctx.wrapper.error('HANDLE_ERROR', '组卷失败');
  // 清空试题篮
  const studyInfo = await studyService.getPeriodSubjectByName(user, period, subject);
  await basketService.deleteBasket(user, studyInfo.period, studyInfo.subject);
  // 添加试题
  const question_ids = [];
  for (const b of algo_paper.blocks) {
    question_ids.push(...b.questions.map(q => q.id));
  }
  // 获取试题
  const kb_questions = await client.kb.getQuestionByIds(question_ids);
  const questions = [];
  for (const id of question_ids) {
    const q = kb_questions.find(e=> e.id === id);
    if (!q) continue;
    questions.push({
      source: enums.QuestionSource.SYS,
      source_id: q.id.toString(),
      type: q.type,
      // period: period,
      // subject: subject
    });
  }
  // 添加试题
  await basketService.pushQuestions(user, studyInfo.period, studyInfo.subject, questions);
  return ctx.wrapper.succ({});
}

function _handler_question_diff(table) {
  const diffArray = ['容易', '较易', '中等', '较难', '困难'];
  for (const block of table.blocks || []) {
    for (const q of block.questions || []) {
      const randomIndex = Math.floor(Math.random() * diffArray.length);
      // 不限难度的暂时随机
      if (q.difficulty === '不限') q.difficulty = diffArray[randomIndex];
    }
  }
}

function _filter_table_empty_knowledges(table) {
  for (const block of table.blocks || []) {
    block.questions = (block.questions || []).filter(e => _.isArray(e.knowledges) && _.size(e.knowledges));
  }
  table.blocks = (table.blocks || []).filter(e => _.isArray(e.questions) && _.size(e.questions));
}

function _exampaper2table(exampaper) {
  var retobj = {};
  retobj.name = exampaper.name;
  retobj.period = exampaper.period;
  retobj.subject = exampaper.subject;
  retobj.province = exampaper.province;
  retobj.type = exampaper.type;
  retobj.grade = exampaper.grade;
  retobj.blocks = [];
  for (const block of exampaper.blocks) {
    const b = {
      type: block.questions[0].type,
      name: block.title
    };
    b.questions = block.questions.map(question => {
      return {
        type: question.type,
        period: question.period,
        subject: question.subject,
        difficulty: mapDiff(question.difficulty),
        knowledges: question.knowledges,
        score: Math.floor(block.score / block.questions.length)
      };
    });
    retobj.blocks.push(b);
  }
  return retobj;
}

function mapDiff(difficult) {
  const diff = Math.floor(difficult / 2);
  switch (diff) {
    case 5:
      return '困难';
    case 4:
      return '困难';
    case 3:
      return '较难';
    case 2:
      return '中等';
    case 1:
      return '较易';
    default:
      return '容易';
  }
}
