module.exports = ({ env }) => {

  const port = env.int('NODE_PORT', 8006)

  const serverConfig = {
    yjCenter: {
      comment: '阅卷中心',
      yjUrl: "https://yx-auth-wan.yunxiao.com",
      appId: "11C13D2D6E44D77D53A027DBA8028C8D9C90F6E2",
      saas: "yuejuan",
      jwtSecret: '7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D',
    },
    openTiku: {
      comment: 'open题库',
      "url": "https://open-tiku-serv-wan.yunxiao.com",
    },
    // 基于 prod 扩展模式配置
    ...{
      prod: {
        serverUrl: `http://jiaoyan-api-lan.yunxiao.com`,
        adminUrl: `http://jiaoyan-admin.yunxiao.com`,
        kbApi: { // kb
          url: 'http://dnr-kb-api-lan.yunxiao.com',
          appKey: 'iyunxiao_tiku20200421'
        },
        algoApi: { // 算法
          url: 'http://new-tiku-algo-lan.yunxiao.com'
        },
        aiQuesApi: {
          url: 'http://ai-ques-serv.yunxiao.com'
        },
        utilBoxApi: {
          url: 'http://dnr-kb-util-lan.yunxiao.com',
          appKey: 'iyunxiao_tiku20200421',
          appid: 'tiku',
          tmpAppid: 'tmp'
        },
        scantron: { // 答题卡
          url: 'https://dtk.haofenshu.com',
          sk: 'IHGGghjklBNJiuytg1567ytfghjuy',
        },
        // wordParseApi: {
        //   url: 'http://dnr-wordparser-lan.yunxiao.com'
        // },
        aiApi: {
          url: 'http://ai-api.iyunxiao.com'
        },
        yjApi: { // 阅卷
          url: 'https://yj-apigw-lan.haofenshu.com',
          appCenterKey: '462fd506cf7c463caa4bdfa94fad5ea3'
        },
        seApi: {
          url: 'http://kb-se-lan.yunxiao.com'
        },
      },
      gray: {
        serverUrl: `http://grayjiaoyan-api-lan.yunxiao.com`,
        adminUrl: `http://grayjiaoyan-admin.yunxiao.com`,
        kbApi: { // kb
          url: 'http://dnr-kb-api-lan.yunxiao.com',
          appKey: 'iyunxiao_tiku20200421'
        },
        algoApi: { // 算法
          url: 'http://new-tiku-algo-lan.yunxiao.com'
        },
        aiQuesApi: {
          url: 'http://ai-ques-serv.yunxiao.com'
        },
        utilBoxApi: {
          url: 'http://dnr-kb-util-lan.yunxiao.com',
          appKey: 'iyunxiao_tiku20200421',
          appid: 'tiku',
          tmpAppid: 'tmp'
        },
        scantron: { // 答题卡
          url: 'https://dtk.haofenshu.com',
          sk: 'IHGGghjklBNJiuytg1567ytfghjuy',
        },
        // wordParseApi: {
        //   url: 'http://dnr-wordparser-lan.yunxiao.com'
        // },
        aiApi: {
          url: 'http://ai-api.iyunxiao.com'
        },
        yjApi: { // 阅卷
          url: 'https://yj-apigw-lan.haofenshu.com',
          appCenterKey: '462fd506cf7c463caa4bdfa94fad5ea3'
        },
        seApi: {
          url: 'http://kb-se-lan.yunxiao.com'
        },
        openTiku: {
          comment: 'open题库',
          "url": "https://grayopen-tiku-serv-wan.yunxiao.com",
        },
      },
      test: {
        serverUrl: `http://testjiaoyan-api-lan.yunxiao.com`,
        adminUrl: `http://testjiaoyan-admin.yunxiao.com`,
        kbApi: {
          url: 'http://dnr-kb-api-lan.yunxiao.com',
          appKey: 'iyunxiao_tiku20200421'
        },
        algoApi: {
          url: 'http://new-tiku-algo-lan.yunxiao.com'
        },
        aiQuesApi: {
          url: 'http://ai-ques-serv.yunxiao.com'
        },
        utilBoxApi: {
          url: 'http://dnr-kb-util-lan.yunxiao.com',
          appKey: 'iyunxiao_tiku20200421',
          appid: 'tiku',
          tmpAppid: 'tmp'
        },
        scantron: { // 答题卡
          url: 'https://dtk.haofenshu.com',
          sk: 'IHGGghjklBNJiuytg1567ytfghjuy',
        },
        // wordParseApi: {
        //   url: 'http://dnr-wordparser-lan.yunxiao.com'
        // },
        aiApi: {
          url: 'http://ai-api.iyunxiao.com'
        },
        yjApi: { // 阅卷
          url: 'https://yj-apigw-lan.haofenshu.com',
          appCenterKey: '462fd506cf7c463caa4bdfa94fad5ea3'
        },
        seApi: {
          url: 'http://devdnr-kbse-wan.yunxiao.com'
        },
        openTiku: {
          comment: 'open题库',
          "url": "https://devopen-tiku-serv-wan.yunxiao.com",
        },
      },
      local: {
        serverUrl: `http://localhost:${port}`,
        adminUrl: `http://localhost:${port + 1}`,
        kbApi: {
          url: 'http://dnr-kb-api-lan.yunxiao.com',
          appKey: 'iyunxiao_tiku20200421'
        },
        algoApi: {
          url: 'http://new-tiku-algo-lan.yunxiao.com'
        },
        aiQuesApi: {
          url: 'http://ai-ques-serv.yunxiao.com'
        },
        utilBoxApi: {
          url: 'http://dnr-kb-util-lan.yunxiao.com',
          appKey: 'iyunxiao_tiku20200421',
          appid: 'tiku',
          tmpAppid: 'tmp'
        },
        scantron: { // 答题卡
          url: 'https://dtk.haofenshu.com',
          sk: 'IHGGghjklBNJiuytg1567ytfghjuy',
        },
        // wordParseApi: {
        //   url: 'http://dnr-wordparser-lan.yunxiao.com'
        // },
        aiApi: {
          url: 'http://ai-api.iyunxiao.com'
        },
        yjApi: { // 阅卷
          url: 'https://yj-apigw-lan.haofenshu.com',
          appCenterKey: '462fd506cf7c463caa4bdfa94fad5ea3'
        },
        seApi: {
          url: 'http://devdnr-kbse-wan.yunxiao.com'
        },
        openTiku: {
          comment: 'open题库',
          "url": "https://devopen-tiku-serv-wan.yunxiao.com",
        },
      },
    }[env('SERVER', 'prod')]
  }

  const webhookConfig = {
    // api报错时的webhook
    prod: {
      webhookUrl: '',
    },
    gray: {
      webhookUrl: '',
    },
    test: {
      webhookUrl: '',
    },
    local: {
      webhookUrl: '',
    },
  }[env('SERVER', 'prod')]

  return {
    host: env('HOST', '0.0.0.0'),
    port: port,
    ...serverConfig,
    webhookUrl: webhookConfig.webhookUrl,
    resetPasswordUrl: serverConfig.resetPasswordUrl,
    admin: {
      serveAdminPanel: false,
      auth: {
        secret: env('ADMIN_JWT_SECRET', 'Rq0JX4TiAH5ECBUpyLGf'),
      },
    },
    jwt: {
      expiresIn: 3600 * 24 * 30
    },
    // 小程序相关配置
    wechat: {
      appid: '',
      secret: '',
      token: ''
    },
    // 短信相关配置 - 当前支持腾讯云短信
    sms: {
      secretId: '',
      secretKey: '',
      region: '',
      SmsSdkAppId: '',
      SignName: ''
    },
    // 验证码相关配置 - 当前支持腾讯云滑动验证码
    captcha: {
      secretId: '',
      secretKey: '',
      CaptchaAppId: 2072317168,
      AppSecretKey: ''
    }
  }
}
