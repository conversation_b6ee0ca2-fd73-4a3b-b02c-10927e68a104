const _ = require('lodash')
const axios = require('axios');
const enums = require('../enums');

module.exports = {
  getPeriodSubjectByName,
  getAcademicYear,
}

async function getPeriodSubjectByName(user, periodName, subjectName) {
  const periodDoc = await strapi.query('period').findOne({pBranch: user.pBranch.id, name: periodName});
  const subjectDoc = await strapi.query('subject').findOne({period: periodDoc.id, name: subjectName});
  return {period: periodDoc.id, subject: subjectDoc.id};
}


/**
 * 获取学年
 * @param date
 * @returns {{to_year: number, from_year: number}}
 */
function getAcademicYear(date = new Date()) {
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 月份从0开始，所以加1

  // 学年从8月开始
  const result = {
    from_year: year,
    to_year: year
  };
  if (month >= 8) {
    result.to_year = year + 1;
  } else {
    result.from_year = year - 1;
  }
  return result;
}
