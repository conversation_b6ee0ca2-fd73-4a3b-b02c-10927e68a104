'use strict'
const { BranchCurdRouter } = require('accel-utils')
const axios = require('axios')
const _ = require('lodash')
const { kb } = require('../../common/client')
const { ObjectId } = require('mongodb')
const book = require('./book')
const { addKnowledges } = require('../services/tree')
const branchCurdRouter = new (class extends BranchCurdRouter {
  async branchUpdate (ctx) {
    const { params, branchId, data } = this._parseBranchCtx(ctx)
    const userId = ctx.state.user.id
    if (data.name) {
      const knowledge = await strapi.query('knowledge').findOne({ id: params.id, }, [])
      data.key = `${data.name}-${knowledge.period}-${knowledge.subject}-${branchId}`
    }
    data.operator = userId
    return super.branchUpdate(ctx)
  }
})('knowledge')

async function addKnowledgeByChapter (ctx) {
  let {
    bookChapter,
    knownledgeTreeChapter,
    knowledges,
  } = ctx.request.body
  if ((!bookChapter && !knownledgeTreeChapter) || (bookChapter && knownledgeTreeChapter) || !knowledges || knowledges.length === 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const branchId = ctx.state.user.pBranch?.id
  const userId = ctx.state.user.id
  if (!branchId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户异常')
  }

  let chapterInfo
  if (bookChapter) {
    chapterInfo = await addKnowledges(bookChapter, userId, knowledges, 'book-chapter')
  } else if (knownledgeTreeChapter) {
    chapterInfo = await addKnowledges(knownledgeTreeChapter, userId, knowledges, 'knowledge-tree-chapter')
  }

  return ctx.wrapper.succ(chapterInfo)
}

async function getKnowledgeListBySearch (ctx) {
  let {
    keyword,
    subject,
    book,
    knowledgeTree,
    bookChapter,
    knowledgeTreeChapter,
    limit = 20,
    start = 0,
  } = ctx.request.query
  if (book || knowledgeTree || bookChapter || knowledgeTreeChapter) {
    let bookKnowledges = [], knowledgeTreeKnowledges = []
    if (bookChapter) {
      let bookChapterInfo = await strapi.query('book-chapter').findOne({
        id: bookChapter,
      }, ['knowledges'])
      if (bookChapterInfo?.knowledges?.length > 0) {
        bookKnowledges = bookChapterInfo.knowledges
      } else {
        let bookChapters = await strapi.query('book-chapter').find({
          path: { $regex: _.escapeRegExp(bookChapterInfo.path), },
          _limit: -1,
        }, ['knowledges'])
        bookKnowledges = bookChapters.map(value => value.knowledges).flat()
      }
    } else if (book) {
      let bookChapters = await strapi.query('book-chapter').find({
        book: book,
        _limit: -1,
      }, ['knowledges'])
      bookKnowledges = bookChapters.map(value => value.knowledges).flat()
    }

    if (knowledgeTreeChapter) {
      let knowledgeTreeChapterInfo = await strapi.query('knowledge-tree-chapter').findOne({
        id: knowledgeTreeChapter,
      }, ['knowledges'])
      if (knowledgeTreeChapterInfo?.knowledges?.length > 0) {
        knowledgeTreeKnowledges = knowledgeTreeChapterInfo.knowledges
      } else {
        let knowledgeTreeChapters = await strapi.query('knowledge-tree-chapter').find({
          path: { $regex: _.escapeRegExp(knowledgeTreeChapterInfo.path), },
          _limit: -1,
        }, ['knowledges'])
        knowledgeTreeKnowledges = knowledgeTreeChapters.map(value => value.knowledges).flat()
      }
    } else if (knowledgeTree) {
      let knowledgeTreeChapters = await strapi.query('knowledge-tree-chapter').find({
        knowledge_tree: knowledgeTree,
        _limit: -1,
      }, ['knowledges'])
      knowledgeTreeKnowledges = knowledgeTreeChapters.map(value => value.knowledges).flat()
    }
    let knowledges = []
    if ((knowledgeTreeChapter || knowledgeTree) && (bookChapter || book)) {
      knowledges = _.intersectionBy(bookKnowledges, knowledgeTreeKnowledges, 'id')
    } else {
      knowledges = bookKnowledges.concat(knowledgeTreeKnowledges)
    }

    if (keyword) {
      knowledges = knowledges.filter(value => new RegExp(keyword).test(value.name))
    }

    return ctx.wrapper.succ({
      total: knowledges.length,
      list: knowledges.slice(start, limit + start),
    })
  } else {
    const query = {
      _start: start,
      _limit: limit,
    }
    if (subject) query.subject = subject
    if (keyword) query.name = { $regex: _.escapeRegExp(keyword) }
    const total = await strapi.query('knowledge').count(query)
    const list = await strapi.query('knowledge').find(query, [])
    return ctx.wrapper.succ({
      total,
      list,
    })
  }
}

module.exports = {
  addKnowledgeByChapter,
  getKnowledgeListBySearch,
  ...branchCurdRouter.createHandlers(),
}