const axios = require('axios');
const URL = require('url');
const qs = require('querystring');
const server = strapi.config.server.utilBoxApi;
const logger = strapi.log;

module.exports = {
    getQuestionAnswerImage,
    getDownloadInfo
}

async function getQuestionAnswerImage(questionId) {
    const url = URL.format({
        host: server.url,
        pathname: `/utilbox_api/v2/questions/${questionId}/image2`,
        search: qs.stringify({
            api_key: server.appKey,
            appid: server.tmpAppid
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(data)}`);
        throw new Error('获取试题信息失败');
    }
    return result.data;
}

async function getDownloadInfo(params) {
    const url = URL.format({
        host: server.url,
        pathname: `/utilbox_api/v1/paper/download`,
        search: qs.stringify({
            api_key: server.appKey,
            // appid: server.tmpAppid
        })
    });
    const result = await axios.post(url, {
        typeset: true,
        math: false,
        size: params.size || 'A4',
        seal: params.seal,
        html: params.html,
        name: params.filename,
        source_plat: 'tiku'
    }, {
        responseType: 'arraybuffer'
    });
    if (!result || result.status !== 200 || !result.data) {
      logger.error(`KB获取试题信息失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(data)}`);
      return null;
    }
    return result;
}


