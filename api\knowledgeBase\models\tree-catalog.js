module.exports = {
  collectionName: 'tree-catalog',
  info: {
    name: 'tree-catalog',
    label: '学校树目录',
    description: '学校树目录'
  },
  options: {
    timestamps: true,
  },
  pluginOptions: {},
  attributes: {
    periodCatalog: {
      label: '学段目录',
      type: 'json',
    },
    bookTreeCatalog: {
      label: '教材树目录',
      type: 'json',
    },
    // knowledgeTreeCatalog: {
    //   label: '知识树目录',
    //   type: 'json',
    // },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
    },
    creator: {
      label: '创建人',
      plugin: 'users-permissions',
      model: 'user',
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
    },
  }
}
