const _ = require('lodash');
const URL = require('url');
const axios = require('axios');
const qs = require('querystring');
const server = strapi.config.server.yjApi;
const logger = strapi.log;
const jwt = require('jsonwebtoken');
const utils = require('../lib/utils');
const enums = require('../enums');

module.exports = {
  getGradeInfo,
  getTeacherInfo,
  getTeacherAuthRoles,
}

/**
 * 获取年级信息
 * @param schoolId
 * @param unifyToken
 * @returns {Promise<null|*>}
 */
async function getGradeInfo(schoolId, unifyToken) {
  const url = URL.format({
    host: server.url,
    // pathname: `/v353/school/gradeGroups`,
    pathname: `/v1/ctb/school/gradeGroups`,
    search: qs.stringify({
      schoolId,
      // unifyToken,
      // usercheckType: 'unify'
    })
  });
  const result = await axios.get(url);
  if (result && result.data && result.data.code === 0) {
    return result.data.data;
  }
  return null;
}

async function getTeacherInfo(id) {
  let tokenKey = Buffer.from(server.appCenterKey, 'base64');
  const token = jwt.sign({ 'removed': true }, tokenKey, {
    algorithm: 'HS512',
    jwtid: id,
    noTimestamp: false
  });
  // const token = await util.promisify(jwt.sign)({ 'removed': true }, tokenKey, {
  //   algorithm: 'HS512',
  //   jwtid: userId.toString(),
  //   noTimestamp: false
  // });

  const url = URL.format({
    host: server.url,
    pathname: '/v353/anno/user/profile/ForAppcenter',
    search: qs.stringify({
      token: token
    })
  });
  const result = await axios.get(url);
  if (!result || result.status !== 200 || !result.data) {
    logger.error(`阅卷获取用户信息失败: url: ${url}, status: ${result.status}`);
    return null;
  }
  return result.data && result.data.data || null;
}

async function getTeacherAuthRoles(id) {
  let result = [];
  const teacher = await getTeacherInfo(id);
  if (!teacher) return result;
  const roleMap = new Map();
  if (teacher.schoolId && teacher.phone) { // 超级管理员
    const admin = `${teacher.schoolId}admin`;
    if (teacher.phone === admin) {
      roleMap.set('admin', {title: '超级管理员'});
    }
  }
  const roles = teacher['[role]'] || [];
  for (const role of roles) {
    let r = roleMap.get(role.title);
    if (!r) {
      r = {
        key: 'role',
        name: role.title,
        children: []
      };
      roleMap.set(role.title, r);
    }
    const nianji = role['[nianji]'][0]; // 映射学段
    if (!nianji) continue;
    const gradeInfo = enums.YjGradeMapping.find(e => e.yj_grade === nianji);
    if (!gradeInfo) continue;
    let period = r.children.find(e => e.name === gradeInfo.period);
    if (!period) {
      period = {
        key: 'period',
        name: gradeInfo.period,
        children: []
      };
      r.children.push(period);
    }
    const xueke = role['[xueke]'][0];
    if (!xueke) continue;
    const standardSubject = utils.getStandardSubject(period.name, xueke);
    if (standardSubject) {
      let subject = period.children.find(e => e.name === standardSubject);
      if (!subject) {
        subject = {
          key: 'subject',
          name: standardSubject
        };
        period.children.push(subject);
      }
    }
  }
  if (roleMap.size > 0) result = Array.from(roleMap.values());
  return result
}
