const axios = require('axios')
const yjCenterConfig = strapi.config.server.yjCenter
const client = require('../../common/client')

// 从阅卷系统获取用户信息
async function getUserInfoByUnifyToken(unifyToken) {
  try {
    const userInfoRes = await axios.post(
      `${yjCenterConfig.yjUrl}/user/query/role`,
      {
        appId: yjCenterConfig.appId,
        saas: yjCenterConfig.saas
      },
      {
        headers: {
          'Cookie': `unify_sid=${unifyToken}`
        }
      }
    )

    if (!userInfoRes?.data) {
      throw new Error('无效的响应数据')
    }

    if (userInfoRes.data.code !== 0) {
      throw new Error(`请求失败: ${userInfoRes.data.message || '未知错误'}`)
    }

    return userInfoRes.data.data
  } catch (error) {
    throw new Error(`获取用户信息失败: ${error.message}`)
  }
}

/**
 * 使用统一认证Token创建用户
 * @param {string} unifyToken 统一认证Token
 * @returns {Promise<{success: boolean, user?: object, error?: string}>} 登录结果
 */
async function processUnifyToken(unifyToken) {
  if (!unifyToken) {
    return { success: false, error: 'Token 无效' }
  }

  const userPlugin = strapi.plugins['users-permissions']
  let userInfo
  try {
    userInfo = await getUserInfoByUnifyToken(unifyToken)
    userInfo.userId = userInfo.userId.toString()
    // 从阅卷系统获取用户信息
    // {
    //   "userId":3976990155112448,
    //   "userName":"爱云校教育局超级管理员",
    //   "schoolId":76659,
    //   "schoolName":"爱云校教育局",
    //   "isAdmin":false,
    //   "schoolRoleType":[],
    //   "disable":false,
    //   "schoolType":"联考"
    // }
  } catch (e) {
    return { success: false, error: 'Token 无效' }
  }

  // 学校转换为租户
  if (!userInfo.schoolId) {
    return { success: false, error: 'Token 无效' }
  }
  let branch = await strapi.query('branch', 'users-permissions').findOne({ yjSchoolId: userInfo.schoolId })
  let updateBranch = {
    name: userInfo.schoolName,
    shortName: userInfo.schoolName,
    type: userInfo.schoolId,
    yjSchoolId: userInfo.schoolId,
  }
  if (!branch) {
    branch = await strapi.query('branch', 'users-permissions').create(updateBranch)
  } else {
    branch = await strapi.query('branch', 'users-permissions').update({ id: branch.id }, updateBranch)
  }

  let user = await strapi.query('user', 'users-permissions').findOne({
    yjUserId: userInfo.userId,
    pBranch: branch.id,
  })

  const allRoles = await strapi.query('role', 'users-permissions').find({})
  const currentRoleTypes = user ? user.roles.map(e => e.type) : []
  const allRoleTypes = ['teacher', ...currentRoleTypes]
  const roles = allRoles.filter(role => allRoleTypes.includes(role.type))
  const userData = {
    yjUserId: userInfo.userId,
    username: userInfo.userName,
    confirmed: true,
    role: roles[0],
    roles: roles,
    yjUserInfo: userInfo,
    pBranch: branch.id,
  }

  if (!user) {
    user = await userPlugin.services['user'].create(userData)
  } else {
    user = await userPlugin.services['user'].edit({ id: user.id }, userData)
  }
  
  return { success: true, user }
}

module.exports = {
  processUnifyToken,
  getUserInfoByUnifyToken
}