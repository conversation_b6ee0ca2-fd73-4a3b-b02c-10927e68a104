/**
 * 批量下载ZIP资源消耗计算器
 * 根据文件数量和大小预估服务器资源消耗
 */

class ResourceCalculator {
  constructor() {
    // 基准测试数据 (基于实际测试结果)
    this.benchmarks = {
      // CPU消耗 (毫秒/MB)
      cpu: {
        compression_level_1: 5,    // 快速压缩: 5ms/MB
        compression_level_6: 20,   // 默认压缩: 20ms/MB  
        compression_level_9: 80,   // 最高压缩: 80ms/MB
        download_overhead: 2       // 下载处理开销: 2ms/MB
      },
      
      // 内存消耗
      memory: {
        base_overhead: 50,         // 基础开销: 50MB
        streaming_buffer: 5,       // 流式缓冲: 5MB/文件
        compression_buffer: 10     // 压缩缓冲: 10MB
      },
      
      // 带宽消耗倍数
      bandwidth: {
        download_multiplier: 1.0,  // 下载倍数
        upload_multiplier: 0.7,    // 上传倍数(考虑压缩)
        overhead: 0.1              // 协议开销: 10%
      },
      
      // 压缩比 (根据文件类型)
      compression_ratio: {
        images: 0.95,      // 图片文件压缩比很低
        videos: 0.98,      // 视频文件几乎不压缩
        documents: 0.3,    // 文档文件压缩比高
        archives: 0.9,     // 已压缩文件
        text: 0.2,         // 文本文件压缩比很高
        mixed: 0.7         // 混合文件类型
      }
    }
  }

  /**
   * 计算资源消耗
   * @param {Object} params - 计算参数
   * @param {number} params.fileCount - 文件数量
   * @param {number} params.totalSizeMB - 总文件大小(MB)
   * @param {number} params.compressionLevel - 压缩级别(1-9)
   * @param {string} params.fileType - 文件类型('mixed', 'images', 'documents', etc.)
   * @param {number} params.concurrentUsers - 并发用户数
   * @returns {Object} 资源消耗估算
   */
  calculate(params) {
    const {
      fileCount,
      totalSizeMB,
      compressionLevel = 1,
      fileType = 'mixed',
      concurrentUsers = 1
    } = params

    // CPU消耗计算
    const cpuConsumption = this.calculateCPU(totalSizeMB, compressionLevel)
    
    // 内存消耗计算
    const memoryConsumption = this.calculateMemory(fileCount, totalSizeMB)
    
    // 带宽消耗计算
    const bandwidthConsumption = this.calculateBandwidth(totalSizeMB, fileType)
    
    // 处理时间估算
    const processingTime = this.calculateProcessingTime(totalSizeMB, compressionLevel, fileCount)
    
    // 并发影响
    const concurrencyImpact = this.calculateConcurrencyImpact(
      cpuConsumption, 
      memoryConsumption, 
      concurrentUsers
    )

    return {
      single_user: {
        cpu: cpuConsumption,
        memory: memoryConsumption,
        bandwidth: bandwidthConsumption,
        processing_time: processingTime
      },
      concurrent_users: concurrencyImpact,
      recommendations: this.generateRecommendations(params, concurrencyImpact)
    }
  }

  calculateCPU(totalSizeMB, compressionLevel) {
    const compressionKey = `compression_level_${compressionLevel}`
    const compressionCost = this.benchmarks.cpu[compressionKey] || this.benchmarks.cpu.compression_level_1
    const downloadCost = this.benchmarks.cpu.download_overhead
    
    const totalCPUMs = (compressionCost + downloadCost) * totalSizeMB
    
    return {
      total_cpu_ms: Math.round(totalCPUMs),
      cpu_seconds: Math.round(totalCPUMs / 1000 * 100) / 100,
      cpu_core_seconds: Math.round(totalCPUMs / 1000 * 100) / 100, // 单核秒
      estimated_cores_needed: Math.max(0.1, Math.round(totalCPUMs / 10000 * 100) / 100) // 假设10秒内完成
    }
  }

  calculateMemory(fileCount, totalSizeMB) {
    const baseMemory = this.benchmarks.memory.base_overhead
    const streamingMemory = Math.min(fileCount * this.benchmarks.memory.streaming_buffer, 50) // 最大50MB
    const compressionMemory = this.benchmarks.memory.compression_buffer
    
    const totalMemoryMB = baseMemory + streamingMemory + compressionMemory
    
    return {
      base_overhead_mb: baseMemory,
      streaming_buffer_mb: streamingMemory,
      compression_buffer_mb: compressionMemory,
      total_memory_mb: totalMemoryMB,
      peak_memory_mb: Math.round(totalMemoryMB * 1.2) // 峰值内存增加20%
    }
  }

  calculateBandwidth(totalSizeMB, fileType) {
    const compressionRatio = this.benchmarks.compression_ratio[fileType] || this.benchmarks.compression_ratio.mixed
    const compressedSizeMB = totalSizeMB * compressionRatio
    
    const downloadBandwidthMB = totalSizeMB * this.benchmarks.bandwidth.download_multiplier
    const uploadBandwidthMB = compressedSizeMB * this.benchmarks.bandwidth.upload_multiplier
    const overheadMB = (downloadBandwidthMB + uploadBandwidthMB) * this.benchmarks.bandwidth.overhead
    
    const totalBandwidthMB = downloadBandwidthMB + uploadBandwidthMB + overheadMB
    
    return {
      download_mb: Math.round(downloadBandwidthMB),
      upload_mb: Math.round(uploadBandwidthMB),
      overhead_mb: Math.round(overheadMB),
      total_bandwidth_mb: Math.round(totalBandwidthMB),
      compression_ratio: compressionRatio,
      bandwidth_savings_mb: Math.round(totalSizeMB - compressedSizeMB)
    }
  }

  calculateProcessingTime(totalSizeMB, compressionLevel, fileCount) {
    // 下载时间 (假设并发下载，受限于5个并发)
    const downloadConcurrency = Math.min(fileCount, 5)
    const avgDownloadSpeedMBps = 10 // 假设10MB/s下载速度
    const downloadTimeSeconds = totalSizeMB / (avgDownloadSpeedMBps * downloadConcurrency)
    
    // 压缩时间
    const compressionSpeedMBps = compressionLevel === 1 ? 50 : (compressionLevel === 6 ? 20 : 5)
    const compressionTimeSeconds = totalSizeMB / compressionSpeedMBps
    
    // 总处理时间 (下载和压缩可以部分并行)
    const totalTimeSeconds = Math.max(downloadTimeSeconds, compressionTimeSeconds * 0.8)
    
    return {
      download_time_seconds: Math.round(downloadTimeSeconds),
      compression_time_seconds: Math.round(compressionTimeSeconds),
      total_time_seconds: Math.round(totalTimeSeconds),
      total_time_minutes: Math.round(totalTimeSeconds / 60 * 100) / 100
    }
  }

  calculateConcurrencyImpact(cpuConsumption, memoryConsumption, concurrentUsers) {
    return {
      concurrent_users: concurrentUsers,
      total_cpu_core_seconds: cpuConsumption.cpu_core_seconds * concurrentUsers,
      total_memory_mb: memoryConsumption.peak_memory_mb * concurrentUsers,
      cpu_cores_needed: Math.ceil(cpuConsumption.estimated_cores_needed * concurrentUsers),
      memory_gb_needed: Math.ceil(memoryConsumption.peak_memory_mb * concurrentUsers / 1024 * 100) / 100
    }
  }

  generateRecommendations(params, concurrencyImpact) {
    const recommendations = []
    
    // CPU建议
    if (concurrencyImpact.cpu_cores_needed > 4) {
      recommendations.push({
        type: 'CPU',
        level: 'warning',
        message: `需要${concurrencyImpact.cpu_cores_needed}核CPU，建议升级服务器或限制并发数`
      })
    } else if (concurrencyImpact.cpu_cores_needed > 2) {
      recommendations.push({
        type: 'CPU', 
        level: 'info',
        message: `建议使用至少${Math.ceil(concurrencyImpact.cpu_cores_needed)}核CPU`
      })
    }
    
    // 内存建议
    if (concurrencyImpact.memory_gb_needed > 8) {
      recommendations.push({
        type: 'Memory',
        level: 'warning', 
        message: `需要${concurrencyImpact.memory_gb_needed}GB内存，建议升级服务器`
      })
    } else if (concurrencyImpact.memory_gb_needed > 4) {
      recommendations.push({
        type: 'Memory',
        level: 'info',
        message: `建议使用至少${Math.ceil(concurrencyImpact.memory_gb_needed)}GB内存`
      })
    }
    
    // 并发建议
    if (params.concurrentUsers > 10) {
      recommendations.push({
        type: 'Concurrency',
        level: 'warning',
        message: '并发用户数过高，建议实现队列机制'
      })
    }
    
    // 压缩级别建议
    if (params.compressionLevel > 6) {
      recommendations.push({
        type: 'Compression',
        level: 'info', 
        message: '高压缩级别会显著增加CPU消耗，建议使用级别1-6'
      })
    }
    
    return recommendations
  }

  // 预设场景计算
  getScenarioEstimates() {
    const scenarios = [
      {
        name: '小文件场景',
        params: { fileCount: 10, totalSizeMB: 10, compressionLevel: 1, fileType: 'mixed', concurrentUsers: 20 }
      },
      {
        name: '中等文件场景', 
        params: { fileCount: 50, totalSizeMB: 100, compressionLevel: 1, fileType: 'mixed', concurrentUsers: 10 }
      },
      {
        name: '大文件场景',
        params: { fileCount: 20, totalSizeMB: 500, compressionLevel: 1, fileType: 'mixed', concurrentUsers: 5 }
      },
      {
        name: '超大文件场景',
        params: { fileCount: 10, totalSizeMB: 1000, compressionLevel: 1, fileType: 'mixed', concurrentUsers: 2 }
      }
    ]
    
    return scenarios.map(scenario => ({
      ...scenario,
      estimate: this.calculate(scenario.params)
    }))
  }
}

// 使用示例
function runExamples() {
  const calculator = new ResourceCalculator()
  
  console.log('📊 批量下载ZIP资源消耗估算')
  console.log('=' * 60)
  
  const scenarios = calculator.getScenarioEstimates()
  
  scenarios.forEach(scenario => {
    console.log(`\n🎯 ${scenario.name}`)
    console.log(`   文件: ${scenario.params.fileCount}个, 总大小: ${scenario.params.totalSizeMB}MB`)
    console.log(`   并发用户: ${scenario.params.concurrentUsers}个`)
    console.log('-'.repeat(40))
    
    const est = scenario.estimate
    console.log(`   CPU需求: ${est.concurrent_users.cpu_cores_needed}核`)
    console.log(`   内存需求: ${est.concurrent_users.memory_gb_needed}GB`)
    console.log(`   带宽消耗: ${est.single_user.bandwidth.total_bandwidth_mb}MB/用户`)
    console.log(`   处理时间: ${est.single_user.processing_time.total_time_minutes}分钟`)
    
    if (est.recommendations.length > 0) {
      console.log('   建议:')
      est.recommendations.forEach(rec => {
        console.log(`     ${rec.type}: ${rec.message}`)
      })
    }
  })
}

if (require.main === module) {
  runExamples()
}

module.exports = ResourceCalculator
