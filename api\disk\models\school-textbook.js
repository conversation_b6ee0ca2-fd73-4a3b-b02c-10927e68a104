module.exports = {
  collectionName: 'school-textbook',
  info: {
    name: 'school-textbook',
    label: '校本教辅',
    description: '校本教辅资源管理'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { name: 1, pBranch: 1 }, options: {} },
      { keys: { period: 1, subject: 1, grade: 1 }, options: {} },
      { keys: { creator: 1 }, options: {} },
      { keys: { status: 1 }, options: {} },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '教辅名称',
      type: 'string',
      required: true,
      maxLength: 200,
    },
    description: {
      label: '教辅描述',
      type: 'text',
      default: '',
    },
    grade: {
      label: '年级',
      type: 'string',
      required: true,
      options: [
        { label: '一年级', value: '一年级' },
        { label: '二年级', value: '二年级' },
        { label: '三年级', value: '三年级' },
        { label: '四年级', value: '四年级' },
        { label: '五年级', value: '五年级' },
        { label: '六年级', value: '六年级' },
        { label: '七年级', value: '七年级' },
        { label: '八年级', value: '八年级' },
        { label: '九年级', value: '九年级' },
        { label: '高一', value: '高一' },
        { label: '高二', value: '高二' },
        { label: '高三', value: '高三' },
      ]
    },
    period: {
      label: '学段',
      type: 'string',
      required: true,
      options: [
        { label: '小学', value: '小学' },
        { label: '初中', value: '初中' },
        { label: '高中', value: '高中' },
      ]
    },
    subject: {
      label: '科目',
      type: 'string',
      required: true,
      options: [
        { label: '语文', value: '语文' },
        { label: '数学', value: '数学' },
        { label: '英语', value: '英语' },
        { label: '物理', value: '物理' },
        { label: '化学', value: '化学' },
        { label: '生物', value: '生物' },
        { label: '地理', value: '地理' },
        { label: '历史', value: '历史' },
        { label: '政治', value: '政治' },
        { label: '道德与法治', value: '道德与法治' },
        { label: '科学', value: '科学' },
        { label: '信息技术', value: '信息技术' },
        { label: '音乐', value: '音乐' },
        { label: '美术', value: '美术' },
        { label: '体育', value: '体育' },
      ]
    },
    cover_url: {
      label: '封面URL',
      type: 'string',
      default: '',
    },
    cover_file: {
      label: '封面文件',
      model: 'disk-file',
    },
    disk_files: {
      label: '网盘文件',
      collection: 'disk-file',
      via: 'school_textbook',
    },
    file_count: {
      label: '文件数量',
      type: 'number',
      default: 0,
    },
    total_size: {
      label: '总文件大小(字节)',
      type: 'number',
      default: 0,
    },
    category: {
      label: '教辅类别',
      type: 'string',
      default: '练习册',
      options: [
        { label: '练习册', value: '练习册' },
        { label: '试卷集', value: '试卷集' },
        { label: '教学课件', value: '教学课件' },
        { label: '教案集', value: '教案集' },
        { label: '学习资料', value: '学习资料' },
        { label: '参考书', value: '参考书' },
        { label: '其他', value: '其他' },
      ]
    },
    tags: {
      label: '标签',
      type: 'json',
      default: [],
      jsonSchema: {
        type: 'array',
        items: {
          type: 'string'
        }
      }
    },
    status: {
      label: '状态',
      type: 'string',
      default: 'draft',
      options: [
        { label: '草稿', value: 'draft' },
        { label: '已发布', value: 'published' },
        { label: '已下架', value: 'archived' },
      ]
    },
    publish_time: {
      label: '发布时间',
      type: 'datetime',
    },
    view_times: {
      label: '浏览次数',
      type: 'number',
      default: 0,
    },
    download_times: {
      label: '下载次数',
      type: 'number',
      default: 0,
    },
    is_featured: {
      label: '是否推荐',
      type: 'boolean',
      default: false,
    },
    is_top: {
      label: '是否置顶',
      type: 'boolean',
      default: false,
    },
    sort_order: {
      label: '排序权重',
      type: 'number',
      default: 0,
    },
    remark: {
      label: '备注',
      type: 'text',
      default: '',
    },
    creator: {
      label: '创建人',
      plugin: 'users-permissions',
      model: 'user',
      required: true,
      configurable: false,
    },
    operator: {
      label: '最后操作人',
      plugin: 'users-permissions',
      model: 'user',
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      required: true,
      configurable: false,
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0,
      options: [
        { label: '正常', value: 0 },
        { label: '已删除', value: 1 },
      ]
    }
  }
}
