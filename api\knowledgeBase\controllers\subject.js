'use strict'
const { BranchCurdRouter } = require('accel-utils')
const axios = require('axios')
const _ = require('lodash')
const branchCurdRouter = new (class extends BranchCurdRouter {
  async branchUpdate (ctx) {
    const { params, branchId, data } = this._parseBranchCtx(ctx)
    const userId = ctx.state.user.id
    if (data.name) {
      const subject = await strapi.query('subject').findOne({ id: params.id, }, [])
      data.key = `${data.name}-${subject.period}-${branchId}`
    }
    data.operator = userId
    return super.branchUpdate(ctx)
  }
})('subject')

module.exports = {
  ...branchCurdRouter.createHandlers(),
}