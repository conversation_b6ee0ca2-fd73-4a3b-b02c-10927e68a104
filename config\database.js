// 使用远程 mongo 数据库
module.exports = ({ env }) => {
  const dbConfig = {
    prod: {
      uri: 'mongodb://tiku_write:<EMAIL>:6010,n01.rs07.yunxiao.io:6010/jzl_jiaoyan?replicaSet=Replset07&readPreference=primaryPreferred&slaveOk=true',
    },
    gray: {
      uri: 'mongodb://tiku_write:<EMAIL>:6010,n01.rs07.yunxiao.io:6010/jzl_jiaoyan?replicaSet=Replset07&readPreference=primaryPreferred&slaveOk=true',
    },
    test: {
      uri: 'mongodb://CTB:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testjzl_jiaoyan',
    },
    local: {
      uri: 'mongodb://localhost:27017/jzl_jiaoyan'
    },
  }[env('DATABASE', 'prod')]
  return {
    defaultConnection: 'default',
    connections: {
      default: {
        connector: 'mongoose',
        settings: {
          client: 'mongo',
          ...dbConfig
        },
        options: {
          authenticationDatabase: dbConfig.database,
          ssl: env('DATABASE_SSL', false),
          debug: env('DATABASE_DEBUG', false),
        },
      },
    },
  }
}
