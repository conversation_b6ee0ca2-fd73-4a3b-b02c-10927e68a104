module.exports = {
  collectionName: 'question',
  info: {
    name: 'question',
    label: '试题',
    description: '试题'
  },
  options: {
    timestamps: true,
    // indexes: [
    //   { keys: { key: 1 }, options: { unique: true } },
    // ],
  },
  pluginOptions: {},
  attributes: {
    type: {
      label: '类型',
      type: 'string',
      required: true,
    },
    period: {
      label: '学段',
      model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      model: 'subject',
      required: true,
    },
    grade: {
      label: '学科',
      type: 'string',
      required: true,
    },
    difficulty: {
      label: '难度',
      type: 'number',
      required: true,
    },
    score: {
      label: '分值',
      type: 'number',
    },
    description: {
      label: '各小题的公共题干部分',
      type: 'string',
      default: ''
    },
    comment: {
      label: '试题点评',
      type: 'string',
      default: ''
    },
    year: {
      label: '学年',
      type: 'number',
      default: 0,
    },
    knowledges: {
      label: '知识点',
      collection: 'knowledge',
      type: 'json',
      jsonSchema: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: {
              title: 'ID',
              type: 'string',
            },
            name: {
              title: '名称',
              type: 'string',
            },
          }
        }
      }
    },
    blocks: {
      label: '试题块',
      type: 'json',
      jsonSchema: {
        type: 'object',
        attributes: {
          types: {
            title: '类型',
            type: 'json',
          },
          explanations: {
            title: '解析：一维数组',
            type: 'json'
          },
          solutions: {
            title: '解答：一维数组',
            type: 'json'
          },
          answers: {
            title: '答案',
            type: 'json',
          },
          stems: { // 包含 题干,选项,音频
            title: '题干-一维数组',
            type: 'json',
          },
          knowledges: {
            title: '对应小题知识点',
            type: 'json',
          }
        }
      }
    },
    audio: {
      label: '音频',
      type: 'json',
      jsonSchema: {
        type: 'object',
        attributes: {
          name: {
            title: '名称',
            type: 'string',
          },
          url: {
            title: '地址',
            type: 'string',
          }
        }
      }
    },
    refer_exampapers: {
      label: '引用试卷',
      type: 'json',
      jsonSchema: {
        type: 'array',
        items: {
          type: 'object',
          attributes: {
            id: {
              title: 'ID',
              type: 'string',
            },
            name: {
              title: '试卷名称',
              type: 'string',
            },
            year: {
              title: '学年开始',
              type: 'number',
            },
            from_year: {
              title: '学年结束',
              type: 'number',
            }, //
            category: {
              title: '试卷类型',
              type: 'string',
            }, // 试卷类型
            province: {
              title: '省',
              type: 'string',
            }, // 省
            city: {
              title: '市',
              type: 'string',
            }, // 市
            region: {
              title: '区',
              type: 'string',
            }, // 区
            grade: {
              title: '年级',
              type: 'string',
            }, // 年级
          }
        }
      }
    },
    refer_times: {
      label: '引用次数',
      type: 'number',
      default: 0,
    },
    source: {
      label: '来源',
      type: 'string',
      required: true,
      options: [
        {
          label: '上传',
          value: 'upload'
        },
        {
          label: '备课资源',
          value: 'resource'
        }
      ]
    },
    source_id: {
      label: '资源ID',
      type: 'string',
      required: true,
    },
    download_times: {
      label: '下载次数',
      type: 'number',
      default: 0,
      required: true
    },
    view_times: {
      label: '浏览次数',
      type: 'number',
      default: 0,
      required: true
    },
    shared: {
      label: '分享标识',
      type: 'number',
      default: 0,
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0,
    },
    user: {
      label: '用户',
      plugin: 'users-permissions',
      model: 'user',
      configurable: false
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
