const _ = require('lodash');
const client = require('../../common/client');
const enums = require('../../common/enums');

module.exports = {
  getInfo,
}

async function getInfo(ctx) {
  const id = ctx.state.user.id;
  let user = await strapi.query('user', 'users-permissions').findOne({id});
  const returnObj = strapi.plugins['users-permissions'].services['user'].getFullAuthData(user);
  returnObj.user = user;
  if (user.yjUserId) user.authRoles = await client.yj.getTeacherAuthRoles(user.yjUserId);
  return ctx.wrapper.succ(returnObj);
}


