module.exports = {
  collectionName: 'basket',
  info: {
    name: 'basket',
    label: '试题篮',
    description: '试题篮'
  },
  options: {
    timestamps: true,
    // indexes: [
    //   { keys: { key: 1 }, options: { unique: true } },
    // ],
  },
  pluginOptions: {},
  attributes: {
    period: {
      label: '学段',
      model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      model: 'subject',
      required: true,
    },
    type: {
      label: '学科',
      type: 'string',
      required: true,
    },
    score: {
      label: '分值',
      type: 'integer',
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'sys',
      options: [
        {
          label: '系统',
          value: 'sys'
        },
        {
          label: '上传',
          value: 'upload'
        },
      ]
    },
    source_id: {
      label: '来源ID',
      type: 'string',
      default: '',
      required: true
    },
    user: {
      label: '用户',
      plugin: 'users-permissions',
      model: 'user',
      configurable: false
    }
  }
}
