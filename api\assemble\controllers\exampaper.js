const { CurdRouter } = require('accel-utils');
const _ = require('lodash');
const Joi = require('joi');
const client = require('../../common/client');
const enums = require('../../common/enums');
const studyService = require('../../common/services/study');
const basketService = require('../services/basket');
const paperService = require('../services/paper');
const questionService = require('../../question/services/question');
const downloadRecordService = require('../../user/services/download-record');
const utils = require('../../common/lib/utils');
const MODEL = 'exampaper';
const curdRouter = new (class extends CurdRouter {
  async find (ctx) {
    const user = ctx.state.user
    if (!['SuperAdmin'].includes(user.role.type)) {
      ctx.request.query.user = user.id;
      ctx.request.query.deleted = enums.Bool.NO;
    }
    return super.find(ctx)
  }
  async count (ctx) {
    const user = ctx.state.user
    if (!['SuperAdmin'].includes(user.role.type)) {
      ctx.request.query.user = user.id;
      ctx.request.query.deleted = enums.Bool.NO;
    }
    return super.count(ctx)
  }
})(MODEL)

module.exports = {
  ...curdRouter.createHandlers(),
  knowledgePaper,
  paperToPaper,
  savePaper,
  getDetail,
  download,
  getDtkGateway,
  deletePaper,
  getExampaperList,
  search,
  addByUpload,
  updateShareStatus,
  refToPerson,
  getAllRef,
}


// const JOI_KNOWLEDGE_PAPER = Joi.object({
//   difficulty: Joi.string().required(),
//   period: Joi.string().required(),
//   subject: Joi.string().required(),
//   province: Joi.string().optional(),
//   city: Joi.string().optional(),
//   type: Joi.string().required(),
//   knowledges_ids: Joi.array().items(Joi.number()).min(1),
//   blocks: Joi.array().items(Joi.object({
//     type: Joi.string().required(),
//     num: Joi.number().required()
//   })).required().min(1)
// });

async function getExampaperList (ctx) {
  const { start = 0, limit = 10, createTime, endTime, periodId, subjectId } = ctx.request.query
  if (!createTime && !endTime && !periodId && !subjectId) return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  const user = ctx.state.user
  const query = {
    user: user.id,
    deleted: 0,
    _limit: limit > 100 ? 100 : limit,
    _start: start,
    _sort: 'createdAt:DESC',
    _projection: { id: 1, name: 1, subtitle: 1, form_year: 1, to_year: 1, createdAt: 1, updatedAt: 1, period: 1, subject: 1, grade: 1, source: 1, source_id: 1, type: 1, status: 1 }
  }
  if (createTime) query.createdAt_gte = new Date(createTime)
  if (endTime) query.createdAt_lte = new Date(endTime)
  if (periodId) query.period = periodId
  if (subjectId) query.subject = subjectId;
  const count = await strapi.query('exampaper').count(query)
  if (count === 0) return ctx.wrapper.succ({
    total: 0,
    list: [],
  })
  const exampapers = await strapi.query('exampaper').find(query, [])

  return ctx.wrapper.succ({
    list: exampapers,
    total: count,
  })
}

async function knowledgePaper(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.body;
  const {knowledges_ids, difficulty, period, subject, blocks} = params;
  const algoDiffMap = {
    '容易': '容易',
    '一般': '中等',
    '普通': '中等',
    '困难': '困难',
    '不限': '不限',
  };
  const knowledges  = await client.kb.getKnowledgeByIds(knowledges_ids);
  let algo_params = {
    period: period,
    subject: subject,
    difficulty: algoDiffMap[difficulty] || '中等',
    knowledges: knowledges,
    ques_num: blocks,
    // province: req.body.province,
    // city: req.body.city
  };

  const algo_paper = await client.algo.knowledgesPaper(algo_params);
  if (_.isEmpty(algo_paper) || !_.size(algo_paper.blocks)) return ctx.wrapper.error('HANDLE_ERROR', '组卷失败');
  // 清空试题篮
  const studyInfo = await studyService.getPeriodSubjectByName(user, period, subject);
  await basketService.deleteBasket(user, studyInfo.period, studyInfo.subject);
  // 添加试题
  const question_ids = [];
  for (const b of algo_paper.blocks) {
    question_ids.push(...b.questions.map(q => q.id));
  }
  // 获取试题
  const kb_questions = await client.kb.getQuestionByIds(question_ids);
  const questions = [];
  for (const id of question_ids) {
    const q = kb_questions.find(e=> e.id === id);
    if (!q) continue;
    questions.push({
      source: enums.QuestionSource.SYS,
      source_id: q.id.toString(),
      type: q.type,
    });
  }
  // 添加试题
  await basketService.pushQuestions(user, studyInfo.period, studyInfo.subject, questions);
  return ctx.wrapper.succ({});
}

//
// const JOI_PAPER_TO_PAPER = Joi.object({
//   id: Joi.number().required(),
// });


async function paperToPaper(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  const kb_exampaper = await client.kb.getPaperById(id);
  if (_.isEmpty(kb_exampaper)) return ctx.wrapper.error('HANDLE_ERROR', '试卷不存在');
  const algo_params = {
    period: kb_exampaper.period,
    subject: kb_exampaper.subject,
    type: '细目表组卷',
    // school_id: user.schoolId,
    user_id: user.id.toString(),
    paper_id: kb_exampaper.id.toString(),
    grade: kb_exampaper.grade,
    blocks: [],
    filtered_ques: []
  }
  for (const b of kb_exampaper.blocks) {
    const block = {
      type: b.type,
      questions: []
    };
    for (const q of b.questions || []) {
      algo_params.filtered_ques.push(q.id);
      if (!_.size(q.knowledges)) continue;
      const knowledges = q.knowledges.map(k => {
        return {
          id: k.id,
          name: k.name
        }
      });
      const question = {
        type: q.type,
        score: q.score,
        difficulty: enums.QuestionDifficultyName[q.difficulty],
        knowledges: knowledges.slice(0, 3)
      }
      block.questions.push(question);
    }
    if (!_.size(block.questions)) continue;
    algo_params.blocks.push(block);
  }
  const algo_paper = await client.algo.detailTablePaper(algo_params);
  if (_.isEmpty(algo_paper) || !_.size(algo_paper.blocks)) return ctx.wrapper.error('HANDLE_ERROR', '组卷失败');
  // 清空试题篮
  const studyInfo = await studyService.getPeriodSubjectByName(user, kb_exampaper.period, kb_exampaper.subject);
  await basketService.deleteBasket(user, studyInfo.period, studyInfo.subject);
  // 添加试题
  const question_ids = [];
  for (const b of algo_paper.blocks) {
    question_ids.push(...b.questions.map(q => q.id));
  }
  // 获取试题
  const kb_questions = await client.kb.getQuestionByIds(question_ids);
  const questions = [];
  for (const id of question_ids) {
    const q = kb_questions.find(e=> e.id === id);
    if (!q) continue;
    questions.push({
      source: enums.QuestionSource.SYS,
      source_id: q.id.toString(),
      type: q.type,
    });
  }
  // 添加试题
  await basketService.pushQuestions(user, studyInfo.period, studyInfo.subject, questions);
  return ctx.wrapper.succ({});
}



const JOI_POST = Joi.object({
  id: Joi.string().optional().length(24),
  name: Joi.string().required(),
  period: Joi.string().required(),
  subject: Joi.string().required(),
  grade: Joi.string().required(),
  type: Joi.string().optional().default('同步练习'),
  subtitle: Joi.string().optional().default('').allow(''),              // 副标题
  score: Joi.number().required(),                 // 试卷总分
  duration: Joi.number().required(),              // 考试时间，单位分钟
  paper_info: Joi.string().required(),            // 试卷信息栏，考试范围：xxx；333考试时间：100分钟；命题人：xxx
  cand_info: Joi.string().required(),             // 候选人信息栏
  from_year: Joi.number().integer().optional(),
  to_year: Joi.number().integer().optional(),
  score_info: Joi.string().optional().default('').allow(''),            // 得分栏
  attentions: Joi.string().optional().default('').allow(''),            // 注意事项
  secret_tag: Joi.string().optional().default('').allow(''),            // 保密标记文字
  gutter: Joi.number().valid(0, 1).required(),                // 1：表示有装订线； 0：表示无装订线
  template: Joi.string().required(),
  parts_list: Joi.array().items(Joi.string()).required().min(1),
  volumes: Joi.array().items(Joi.object({
    title: Joi.string().required(),
    note: Joi.string().optional().default('').allow(''),
    blocks: Joi.array().items(Joi.object({
      title: Joi.string().required(),
      note: Joi.string().optional().default('').allow(''),
      type: Joi.string().required(),
      default_score: Joi.number().required(),
      questions: Joi.array().items(Joi.object({
          id: Joi.any().required(),
          score: Joi.number().required(),
          source: Joi.string().required(),
          source_id: Joi.string().required(),
        }).unknown(true)
      )
    }))
  })).required().min(1).max(3),
  status: Joi.string().optional().valid(enums.PaperStatus.EDIT, enums.PaperStatus.DONE), // 状态
});

// const fields = [
//   'id', 'period', 'subject', 'grade', 'name', 'subtitle', 'type',
//   'score', 'duration', 'paper_info', 'cand_info', 'score_info', 'attentions', 'secret_tag', 'gutter',
//   'template', 'parts_list', 'from_year', 'to_year', 'volumes', 'status'
// ];


async function savePaper(ctx) {
  const user = ctx.state.user;
  const {error, value} = JOI_POST.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const params = _.assign({}, value);
  const {id, status} = params;
  let paper = {};
  if (id) {
    paper = await strapi.query(MODEL).findOne({id: id, user: user.id});
    if (_.isEmpty(paper)) return ctx.wrapper.error('HANDLE_ERROR', '试卷不存在');
    if (status && paper.status !== enums.PaperStatus.EDIT) return ctx.wrapper.error('HANDLE_ERROR', '试卷不可编辑');
  }

  // 重新设置学段科目
  const studyInfo = await studyService.getPeriodSubjectByName(user, params.period, params.subject);
  params.period = studyInfo.period;
  params.subject = studyInfo.subject;
  params.ques_num = _getQuestionNum(params);
  if (id) {
    if (status === enums.PaperStatus.DONE) {
      params.status = paper.status; //
      params.shared = paper.shared;
      await questionService.syncPaperQuestions(user, params);
      params.status = status;
    }
    delete params.id;
    delete params.action;
    const upPaper = await strapi.query(MODEL).update({id: id}, params);
    if (upPaper.status === enums.PaperStatus.DONE && upPaper.shared) {
      await questionService.syncPaperQuestionShareStatus(user, upPaper);
      await questionService.syncPaperQuestionRef(user, upPaper);
    }
    return ctx.wrapper.succ({id});
  } else { // 组卷
    const {from_year, to_year} = studyService.getAcademicYear();
    if (!params.from_year) params.from_year = from_year;
    if (!params.to_year) params.to_year = to_year;
    params.source = enums.PaperSourceType.ASSEMBLE;
    params.source_id = enums.PaperSourceType.ASSEMBLE;
    params.status = enums.PaperStatus.INIT;
    params.shared = enums.Bool.NO;
    params.user = user.id;
    params.pBranch = user.pBranch.id;
    // 同步试题
    await questionService.syncPaperQuestions(user, params);
    params.status = enums.PaperStatus.DONE;
    const newData = await strapi.query(MODEL).create(params);
    await questionService.syncPaperQuestionRef(user, newData);
    return ctx.wrapper.succ({id: newData.id});
  }
}

async function getDetail(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  const paper = await strapi.query(MODEL).findOne({id, pBranch: user.pBranch.id});
  paper.period = paper.period.name;
  paper.subject = paper.subject.name;
  if (_.isEmpty(paper)) return ctx.wrapper.error('HANDLE_ERROR', '试卷不存在');
  if (paper.status === enums.PaperStatus.ERROR) return ctx.wrapper.succ({});
  if (paper.status === enums.PaperStatus.DONE) {
    const ids = [];
    for (const volume of paper.volumes || []) {
      for (const block of volume.blocks || []) {
        ids.push(...block.questions.map(e => e.id));
      }
    }
    const questions = await questionService.getByIds(user, ids);
    await basketService.traverseQuestions(paper, _.keyBy(questions, 'id'));
  }
  // 处理试题题号和去除题内题号
  paperService.process(paper);
  // 字段转换
  delete paper.deleted;
  paper.user = utils.pickFields(paper.user, ['id', 'username']);
  paper.pBranch = utils.pickFields(paper.pBranch, ['id', 'name']);
  if (paper.status === enums.PaperStatus.DONE) { // 完成状态更新试卷浏览次数
    paper.view_times = paper.view_times + 1;
    await paperService.incTimes(paper.id, enums.ActionType.VIEW_TIMES);
  }
  return ctx.wrapper.succ(paper);
}

// 下载
async function download(ctx) {
  const user = ctx.state.user;
  const {id, content, source = enums.PaperSourceType.SYS } = ctx.request.body;
  let paper = {};
  if (source === enums.PaperSourceType.ASSEMBLE) {
    paper = await strapi.query(MODEL).findOne({id, user: user.id});
    await paperService.incTimes(id, enums.ActionType.DOWNLOAD_TIMES);
  } else {
    paper = await client.kb.getPaperById(id);
  }
  if (_.isEmpty(paper)) return ctx.wrapper.error('HANDLE_ERROR', '试卷不存在');
  // 获取文件流
  const download_params = {
    html: content,
    filename: paper.name,
  }
  const response = await client.utilbox.getDownloadInfo(download_params);
  // 生成下载记录
  await downloadRecordService.add(user, source === enums.PaperSourceType.ASSEMBLE ? enums.DownloadResource.EXAMPAPER : enums.DownloadResource.SYS_EXAMPAPER, paper);
  // 响应数据
  ctx.set('Content-disposition', `attachment; filename=${encodeURI(`${paper.name}.docx`)}`);
  // res.setHeader('Content-type', fileMime);
  ctx.set('Content-type', 'application/octet-stream');
  ctx.body = response.data;
}



async function getDtkGateway(ctx) {
  const userInfo = ctx.state.user.yjUserInfo;
  if (_.isEmpty(userInfo)) return ctx.wrapper.error('HANDLE_ERROR', '用户信息不完善');
  const result = await client.dtk.getGateWay(userInfo.userId, userInfo.userName, userInfo.schoolId, userInfo.schoolName);
  return ctx.wrapper.succ(result);
}

async function deletePaper(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  const data = await strapi.query(MODEL).findOne({id, pBranch: user.pBranch.id, deleted: enums.Bool.NO});
  if (_.isEmpty(data)) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  if (data.user.id !== user.id) { // 权限校验 超级管理员、年级主任、校领导
    const userRoles = user?.yjUserInfo?.schoolRoleType || [];
    if (!_.size(userRoles) || _.difference(enums.AdminRoles, userRoles).length === enums.AdminRoles.length)
      return ctx.wrapper.error('HANDLE_ERROR', '暂无权限');
  }
  await strapi.query(MODEL).update({id}, {deleted: enums.Bool.YES});
  return ctx.wrapper.succ({id});
}

// 获取所有的引用
async function getAllRef(ctx) {
  const user = ctx.state.user;
  const {period, subject} = ctx.request.query;
  const list = await strapi.query(MODEL, []).find({user: user.id, period, subject, source: enums.ResourceFrom.REF, deleted: enums.Bool.NO});
  const ids = list.map(e => e.source_id);
  return ctx.wrapper.succ(ids);
}

// 获取学科空间的试卷
const JOI_SEARCH = Joi.object({
  offset: Joi.number().integer().required(),
  limit: Joi.number().integer().required(),
  name: Joi.string().optional().allow(''),
  type: Joi.string().optional().allow(''),
  period: Joi.string().required(),
  subject: Joi.string().required(),
  grade: Joi.string().optional().allow(''),
  year: Joi.number().integer().optional(),
  sort_by: Joi.string().optional().default('time'),
  space: Joi.string().optional().allow(''),
  source: Joi.string().optional().allow(''), // 来源
  status: Joi.string().optional().allow(''), // 来源
});

async function search(ctx) {
  const { error, value } = JOI_SEARCH.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;

  const query = {
    period: value.period,
    subject: value.subject,
    // status: enums.PaperStatus.DONE,
    deleted: enums.Bool.NO,
    _limit: value.limit,
    _start: value.offset,
    _sort: 'createdAt:DESC',
    _projection: {
      id: 1, name: 1, subtitle: 1, createdAt: 1, updatedAt: 1, period: 1, subject: 1, grade: 1, user: 1,
      type: 1, shared: 1, ques_num: 1, from_year: 1, to_year: 1, status: 1,
      download_times: 1, view_times: 1, source: 1, error: 1
    }
  }
  if (value.name) query.name_contains = value.name;
  if (value.type) query.type = value.type;
  if (value.grade) query.grade = value.grade;
  if (value.year) {
    if (value.year > 0) query.to_year = value.year;
    else query.to_year_lt = 2020;
  }
  if (value.space) {
    query.status = enums.PaperStatus.DONE;
    if (value.space === enums.Space.SCHOOL) query.pBranch = user.pBranch.id;
    else query.user = user.id;
  } else {
    query.source = enums.PaperSourceType.UPLOAD;
  }
  if (value.source === enums.PaperSourceType.UPLOAD) {
    query.source = value.source;
    delete query.status;
  }
  if (value.status) query.status = value.status;
  if (value.sort_by === 'view_times') query._sort = 'view_times:DESC';
  const result = {
    total: 0,
    list: []
  };
  result.total = await strapi.query(MODEL).count(query);
  if (!result.total) return ctx.wrapper.succ(result);
  result.list = await strapi.query(MODEL).find(query);
  result.list.forEach(e => {
    e.period = utils.pickFields(e.period);
    e.subject = utils.pickFields(e.subject);
    e.user = utils.pickFields(e.user, ['id', 'username']);
    e.pBranch = utils.pickFields(e.pBranch, ['id', 'name']);
  });
  return ctx.wrapper.succ(result);
}

const JOI_ID = Joi.object({
  id: Joi.string().length(24).required()
});

async function updateShareStatus(ctx) {
  const { error, value } = JOI_ID.validate(ctx.params);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const {id} = value;
  const user = ctx.state.user;
  const data = await strapi.query(MODEL).findOne({id, user: user.id});
  if (_.isEmpty(data) || data.status !== enums.PaperStatus.DONE) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  let update = {shared: data.shared ? enums.Bool.NO : enums.Bool.YES};
  update = await strapi.query(MODEL).update({id}, update);
  await questionService.syncPaperQuestionShareStatus(user, update);
  return ctx.wrapper.succ({id});
}

async function refToPerson(ctx) {
  const { error, value } = JOI_ID.validate(ctx.params);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const {id} = value;
  const user = ctx.state.user;
  const data = await strapi.query(MODEL).findOne({id, shared: enums.Bool.YES, pBranch: user.pBranch.id, deleted: enums.Bool.NO});
  if (_.isEmpty(data) || data.user.id === user.id || data.status !== enums.PaperStatus.DONE) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  const newData = _.pick(data, [
    'name', 'subtitle', 'type', 'period', 'subject', 'grade', 'from_year', 'to_year',
    'score', 'duration', 'paper_info', 'cand_info', 'attentions', 'secret_tag', 'score_info', 'gutter', 'parts_list', 'template', 'volumes', 'ques_num'
  ]);
  newData.source = enums.PaperSourceType.REF;
  newData.source_id = data.id;
  newData.status = enums.PaperStatus.DONE;
  newData.user = user.id;
  newData.pBranch = user.pBranch.id;
  const doc = await strapi.query(MODEL).create(newData);
  return ctx.wrapper.succ({id: doc.id});
}

const JOI_ADD_PAPER_BY_UPLOAD = Joi.object({
  name: Joi.string().required(),
  period: Joi.string().required(),
  subject: Joi.string().required(),
  grade: Joi.string().required(),
  type: Joi.string().required(),
  from_year: Joi.number().integer().required(),
  to_year: Joi.number().integer().required(),
  source_url: Joi.string().required(),
  shared: Joi.number().integer().optional().default(enums.Bool.NO)
});

async function addByUpload(ctx) {
  const { error, value } = JOI_ADD_PAPER_BY_UPLOAD.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  value.source = enums.PaperSourceType.UPLOAD;
  value.source_id = enums.PaperSourceType.UPLOAD;
  value.status = enums.PaperStatus.INIT;
  value.deleted = enums.Bool.NO;
  value.user = user.id;
  value.pBranch = user.pBranch.id;
  delete value.url;
  const doc = await strapi.query(MODEL).create(value);
  // 解析试卷
  wordParsePaper(user, doc);
  return ctx.wrapper.succ({id: doc.id});
}

async function wordParsePaper(user, doc) {
  try {
    const result = await client.file.wordParse(doc.period.name, doc.subject.name, doc.type, doc.name, doc.source_url);
    const {code, msg, data} = result;
    if (code !== 0) { // 解析失败
      await strapi.query(MODEL).update({id: doc.id}, {status: enums.PaperStatus.ERROR, error: msg});
      return;
    }
    // 处理解析后的试卷
    const basket = await basketService.initBasket(user.id);
    let index = Date.now();
    const quesMap = {};
    const fun = async (content) => {
      if (!_.isEmpty(content) && content.type === 'question') {
        const ques = {
          id: `upload_${index}`,
          source: enums.QuestionSource.UPLOAD,
          source_id: `upload`,
          period: doc.period.name,
          subject: doc.subject.name,
          grade: doc.grade,
          ...(_.pick(content.content, ['blocks', 'description', 'type', 'comment']))
        }
        quesMap[ques.source_id] = ques;
        index++;
        await basketService.insertQuestion(basket, ques);
      }
      for (const c of content.children || []) {
        await fun(c);
      }
    }
    for (const cont of data.content) {
      await fun(cont);
    }
    const ques_num = _getQuestionNum(basket);
    if (!ques_num) { //
      await strapi.query(MODEL).update({id: doc.id}, {status: enums.PaperStatus.ERROR, error: '未获取到试题'});
      return;
    }
    await basketService.traverseQuestions(basket, quesMap);
    await basketService.renderBasket(basket);
    const upDoc = _.pick(basket, [
      'subtitle', 'score', 'duration', 'paper_info', 'cand_info', 'attentions', 'secret_tag', 'score_info', 'gutter', 'parts_list', 'template',
    ]);
    delete upDoc.period;
    delete upDoc.subject;
    delete upDoc.grade;
    delete upDoc.name;
    upDoc.volumes = basket.volumes;
    upDoc.status = enums.PaperStatus.EDIT;
    upDoc.ques_num = ques_num;
    await strapi.query(MODEL).update({id: doc.id}, upDoc);
  } catch (e) {
    strapi.log.error('试卷解析失败', e);
    await strapi.query(MODEL).update({id: doc.id}, {status: enums.PaperStatus.ERROR, error: e.message});
  }
}

function _getQuestionNum(paper) {
  let num = 0;
  if (_.isEmpty(paper)) return num;
  for (const volume of paper.volumes) {
    for (const block of volume.blocks) {
      num += _.size(block.questions);
    }
  }
  return num;
}


