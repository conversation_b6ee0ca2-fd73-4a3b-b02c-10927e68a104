module.exports = {
  collectionName: 'parse-task',
  info: {
    name: 'parse-task',
    label: '解析任务',
    description: '解析任务'
  },
  options: {
    timestamps: true,
    indexes: [
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      type: 'string',
      required: true,
    },
    disk_files: {
      label: '解析任务',
      collection: 'disk-file',
    },
    period: {
      label: '学段',
      type: 'string',
      required: true,
    },
    subject: {
      label: '科目',
      type: 'string',
      required: true,
    },
    grade:  {
      label: '年级',
      type: 'string',
      required: true,
    },
    type: {
      label: '类型',
      type: 'string',
      required: true,
    },
    from_year: {
      label: '起始年份',
      type: 'number',
      required: true,
    },
    to_year: {
      label: '结束年份',
      type: 'number',
      required: true,
    },
    status: {
      label: '状态',
      type: 'string',
      default: 'upload',
      options: [
        {
          label: '划题中',
          value: 'init'
        },
        {
          label: '待复核',
          value: 'edit'
        },
        {
          label: '已完成',
          value: 'done'
        },
        {
          label: '解析失败',
          value: 'error'
        },
      ]
    },
    task_type: {
      label: '任务类型',
      type: 'string',
      default: 'word',
      options: [
        {
          label: 'word解析',
          value: 'word'
        },
        {
          label: '试卷图片解析',
          value: 'images'
        },
      ]
    },

    error: {
      label: '错误信息',
      type: 'json',
    },
    school_id: {
      label: '分享标识',
      type: 'number',
      required: true
    },
    user_id: {
      label: '用户ID',
      type: 'string',
      required: true
    },
    tiku_paper_id:  {
      label: '题库试卷ID',
      type: 'string',
      required: true
    },
    creator: {
      label: '创建人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0
    }
  }
}
