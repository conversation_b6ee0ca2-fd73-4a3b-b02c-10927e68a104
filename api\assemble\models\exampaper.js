module.exports = {
  collectionName: 'exampaper',
  info: {
    name: 'exampaper',
    label: '组卷',
    description: '用户组卷表'
  },
  options: {
    timestamps: true,
    // indexes: [
    //   { keys: { key: 1 }, options: { unique: true } },
    // ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '试卷名称',
      type: 'string',
      required: true
    },
    subtitle: {
      label: '副标题',
      type: 'string',
      default: ''
    },
    type: {
      label: '试卷类型',
      type: 'string',
      default: '同步练习'
    },
    period: {
      label: '学段',
      model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      model: 'subject',
      required: true,
    },
    grade: {
      label: '年级',
      type: 'string',
    },
    score: {
      label: '考试总分',
      type: 'integer',
    },
    duration: {
      label: '考试时间',
      type: 'integer',
    },
    paper_info: {
      label: '考试信息栏',
      type: 'string',
    },
    cand_info: {
      label: '考生输入框',
      type: 'string',
    },
    from_year: {
      label: '学年-开始',
      type: 'number',
    },
    to_year: {
      label: '学年-开始',
      type: 'number',
    },
    attentions: {
      label: '注意事项',
      type: 'string',
    },             //
    secret_tag: {
      label: '密封标识',
      type: 'string',
    },
    score_info: {
      label: '得分栏',
      type: 'string',
      default: ''
    },
    gutter: {
      label: '装订线',
      type: 'integer',
    },
    parts_list: {
      label: '展示项',
      type: 'json',
      jsonSchema: {
        type: 'array',
        items: {
          type: 'string'
        }
      }
    },
    template: {
      label: '模板',
      type: 'string',
      default: 'homework',
      options: [
        {
          label: '标准',
          value: 'standard'
        },
        {
          label: '测试',
          value: 'exam'
        },
        {
          label: '作业',
          value: 'homework'
        },
      ]
    },
    volumes: {
      label: '分卷',
      type: 'json',
      jsonSchema: {
        type: 'array',
        title: '分卷列表',
        items: {
          type: 'object',
          properties: {
            title: {
              title: '标题',
              type: 'string',
            },
            note: {
              title: '标题',
              type: 'string',
            },
            blocks: {
              title: '试卷分块',
              type: 'json',
              jsonSchema: {
                title: '试卷分块列表',
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    title: {
                      title: '分块标题',
                      type: 'string',
                    },
                    note: {
                      title: '分块说明',
                      type: 'string',
                    },
                    type: {
                      title: '分块试题类型',
                      type: 'string',
                    },   //
                    default_score: {
                      title: '默认分值',
                      type: 'integer',
                    },
                    questions: {
                      title: '试题',
                      type: 'json',
                    }
                  }
                }
              }
            },
          }
        }
      }
    },
    ques_num: {
      label: '试题数量',
      type: 'number',
      default: 0
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'assemble',
      options: [
        {
          label: '用户组卷',
          value: 'assemble'
        },
        {
          label: '上传',
          value: 'upload'
        },
        {
          label: '引用',
          value: 'ref'
        },
      ]
    },
    source_id: {
      label: '来源ID',
      type: 'string',
      default: 'assemble'
    },
    source_url: {
      label: '文件地址',
      type: 'string',
      default: '',
    },
    status: {
      label: '状态',
      type: 'string',
      default: 'init',
      options: [
        {
          label: '划题中',
          value: 'init'
        },
        {
          label: '复核',
          value: 'edit'
        },
        {
          label: '完成',
          value: 'done'
        },
        {
          label: '错误',
          value: 'error'
        }
      ]
    },
    error: {
      label: '错误消息',
      type: 'string',
    },
    user: {
      label: '用户',
      plugin: 'users-permissions',
      model: 'user',
      configurable: false
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
    download_times: {
      label: '下载次数',
      type: 'number',
      default: 0,
      required: true
    },
    view_times: {
      label: '浏览次数',
      type: 'number',
      default: 0,
      required: true
    },
    shared: {
      label: '分享标识',
      type: 'number',
      default: 0,
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0,
    },
  }
}
