const { <PERSON>urd<PERSON>out<PERSON> } = require('accel-utils');
const _ = require('lodash');
const Joi = require('joi');
const client = require('../../common/client');
const enums = require('../../common/enums');
const studyService = require('../../common/services/study');
const questionService = require('../services/question');
const utils = require('../../common/lib/utils');
const MODEL = 'question';
const curdRouter = new CurdRouter(MODEL);


module.exports = {
  ...curdRouter.createHandlers(),
  search,
}

const JOI_SEARCH = Joi.object({
  offset: Joi.number().integer().required(),
  limit: Joi.number().integer().required(),
  period: Joi.string().required(),
  subject: Joi.string().required(),
  grade: Joi.string().optional().allow(''),
  type: Joi.string().optional().allow(''),
  difficulty: Joi.string().optional().allow(''),
  year: Joi.number().integer().optional(),
  knowledges: Joi.array().items(Joi.string()).optional(),
  category: Joi.string().optional().allow(''), // 题源
  sort: Joi.string().required(),
  space: Joi.string().optional().default(enums.Space.SCHOOL),
});

// 试题搜索
async function search(ctx) {
  const {error, value} = JOI_SEARCH.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const result = {
    total: 0,
    list: []
  }
  const params = buildSeParams(user, value);
  const seResult = await client.se.search(params);
  if (!seResult || !seResult.total) return ctx.wrapper.succ(result);
  result.total = seResult.total;
  result.list = await questionService.getByIds(user, seResult.datas);
  return ctx.wrapper.succ(result);
}

function buildSeParams(user, params) {
  const result = {
    offset: params.offset,
    limit: params.limit,
    period: params.period,
    subject: params.subject,
    deleted: enums.Bool.NO
  };
  if (params.grade) result.grade = params.grade;
  if (params.type) result.type = params.type;
  if (params.difficulty) result.difficulty = params.difficulty;
  if (params.year) {
    if (params.year < 0) {
      result.year = [2019, 2018, 2017, 2016, 2015];
    } else {
      result.year = [params.year];
    }
  }
  if (params.knowledges) result.knowledges = params.knowledges;
  if (params.category) result.category = params.category;
  if (params.space === enums.Space.SCHOOL) {
    result.shared = enums.Bool.YES;
    result.pBranch = user.pBranch.id;
  } else {
    result.user = user.id;
  }
  if (params.sort_by === 'time') result.sort = [['createdAt', 'DESC']];
  if (params.sort_by === 'refer_times') result.sort = [['refer_times', 'DESC'], ['createdAt', 'DESC']];
  return result;
}
