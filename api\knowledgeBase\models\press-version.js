module.exports = {
  collectionName: 'press-version',
  info: {
    name: 'press-version',
    label: '教材版本',
    description: '教材版本'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { key: 1 }, options: { unique: true, sparse: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '教材版本',
      type: 'string',
      required: true,
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'manual',
      options: [
        {
          label: '同步',
          value: 'sync'
        },
        {
          label: '手动',
          value: 'manual'
        }
      ]
    },
    key: {
      label: 'key', // name-subject
      required: true,
      type: 'string',
    },
    period: {
      label: '学段',
      model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      model: 'subject',
      required: true,
    },
    books: {
      label: '教材',
      collection: 'book',
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
