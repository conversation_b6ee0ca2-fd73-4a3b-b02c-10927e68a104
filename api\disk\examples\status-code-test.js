/**
 * 专门测试文件批量下载ZIP接口的状态码
 */

const axios = require('axios')

// 配置
const config = {
  baseURL: 'http://localhost:1337',
  token: 'your-jwt-token-here', // 替换为实际的JWT token
  taskId: '6862692570f8790d701dbc2b' // 使用实际的任务ID
}

async function testStatusCode() {
  console.log('🔍 测试文件批量下载ZIP接口状态码...')
  console.log('='.repeat(50))

  try {
    console.log('📋 测试参数:')
    console.log(`  服务器: ${config.baseURL}`)
    console.log(`  任务ID: ${config.taskId}`)
    console.log(`  Token: ${config.token.substring(0, 20)}...`)
    console.log('')

    console.log('🚀 发送请求...')
    const startTime = Date.now()

    const response = await axios.post(
      `${config.baseURL}/parse-tasks/actions/downloadZip`,
      {
        id: config.taskId,
        zipName: 'status-test.zip',
        timeout: 30000
      },
      {
        responseType: 'stream',
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Content-Type': 'application/json'
        },
        timeout: 35000,
        // 不要自动处理错误状态码，我们要检查所有状态码
        validateStatus: () => true
      }
    )

    const endTime = Date.now()

    console.log('📊 响应信息:')
    console.log(`  HTTP状态: ${response.status}`)
    console.log(`  状态文本: ${response.statusText}`)
    console.log(`  响应时间: ${endTime - startTime}ms`)
    console.log(`  Content-Type: ${response.headers['content-type']}`)
    console.log(`  Content-Disposition: ${response.headers['content-disposition']}`)
    console.log('')

    // 详细的状态码分析
    console.log('🔍 状态码分析:')
    switch (response.status) {
      case 200:
        console.log('  ✅ 200 OK - 请求成功，正在返回ZIP文件')
        
        // 验证响应头
        if (response.headers['content-type'] === 'application/zip') {
          console.log('  ✅ Content-Type 正确')
        } else {
          console.log(`  ❌ Content-Type 错误: ${response.headers['content-type']}`)
        }
        
        if (response.headers['content-disposition']?.includes('attachment')) {
          console.log('  ✅ Content-Disposition 正确')
        } else {
          console.log(`  ❌ Content-Disposition 错误: ${response.headers['content-disposition']}`)
        }
        
        // 计算文件大小
        let totalSize = 0
        response.data.on('data', chunk => {
          totalSize += chunk.length
        })

        response.data.on('end', () => {
          console.log(`  ✅ 文件大小: ${(totalSize / 1024).toFixed(2)} KB`)
          console.log('')
          console.log('🎉 状态码测试通过! 接口正常返回200状态码')
        })

        response.data.on('error', (err) => {
          console.error('  ❌ 流处理错误:', err.message)
        })
        break

      case 404:
        console.log('  ❌ 404 Not Found - 接口返回404，可能是以下原因:')
        console.log('    1. 函数没有明确的返回值')
        console.log('    2. ctx.respond = false 但状态码未设置')
        console.log('    3. 路由配置问题')
        break

      case 400:
        console.log('  ❌ 400 Bad Request - 请求参数错误')
        break

      case 401:
        console.log('  ❌ 401 Unauthorized - 认证失败，请检查JWT token')
        break

      case 403:
        console.log('  ❌ 403 Forbidden - 权限不足')
        break

      case 500:
        console.log('  ❌ 500 Internal Server Error - 服务器内部错误')
        break

      default:
        console.log(`  ⚠️  未预期的状态码: ${response.status}`)
    }

  } catch (error) {
    console.log('❌ 请求失败')
    
    if (error.response) {
      console.log(`  HTTP状态: ${error.response.status}`)
      console.log(`  状态文本: ${error.response.statusText}`)
      
      // 尝试读取错误响应
      if (error.response.headers['content-type']?.includes('application/json')) {
        try {
          console.log(`  错误信息: ${JSON.stringify(error.response.data)}`)
        } catch (e) {
          console.log('  无法解析错误响应')
        }
      }
    } else if (error.code === 'ECONNABORTED') {
      console.log(`  错误类型: 请求超时`)
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`  错误类型: 连接被拒绝，请确保服务器正在运行`)
    } else {
      console.log(`  错误信息: ${error.message}`)
    }
    
    console.log('')
    console.log('🔧 故障排除建议:')
    console.log('  1. 确保服务器正在运行 (npm run develop)')
    console.log('  2. 检查JWT token是否有效')
    console.log('  3. 确认解析任务ID存在且有权限访问')
    console.log('  4. 检查任务是否有关联的文件')
    console.log('  5. 查看服务器日志获取详细错误信息')
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testStatusCode().catch(console.error)
}

module.exports = { testStatusCode }
