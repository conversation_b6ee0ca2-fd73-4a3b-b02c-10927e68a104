module.exports = {
  collectionName: 'book',
  info: {
    name: 'book',
    label: '教材树',
    description: '教材树'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { key: 1 }, options: { unique: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '教材树名称',
      type: 'string',
      required: true,
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'manual',
      options: [
        {
          label: '同步',
          value: 'sync'
        },
        {
          label: '手动',
          value: 'manual'
        }
      ]
    },
    source_id: {
      label: '来源id',
      type: 'number',
    },
    key: {
      label: 'key', // name-press_version-pBranch
      required: true,
      type: 'string',
    },
    book_chapter: {
      label: '教材目录',
      model: 'book-chapter',
      required: true,
    },
    children: {
      label: '教材章节',
      type: 'json'
    },
    // children: [{
    //   name: string,         // 第1章 集合与函数概念
    //   id: int,
    //   children: [{
    //     name: string,          // 1.1 集合
    //     id: int
    //       children: [{
    //       name: string,      // 1.1.1 集合的含义与表示
    //       id: int
    //     }],
    //   }],
    // }],
    period: {
      label: '学段',
      type: 'string',
      // model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      type: 'string',
      // model: 'subject',
      required: true,
    },
    press_version: {
      label: '教材版本',
      type: 'string',
      // model: 'press-version',
      required: true,
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
