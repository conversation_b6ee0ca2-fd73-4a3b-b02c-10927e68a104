module.exports = {
  collectionName: 'book-chapter',
  info: {
    name: 'book-chapter',
    label: '教材章节',
    description: '教材章节'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { key: 1 }, options: { unique: true, sparse: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '教材章节名称',
      type: 'string',
      required: true,
    },
    path: {
      label: '路径', //格式：学段id-学科id-版本id-bookid-id1-id2-id
      type: 'string',
      required: true,
    },
    knowledges: {
      label: '知识点',
      collection: 'knowledge',
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'manual',
      options: [
        {
          label: '同步',
          value: 'sync'
        },
        {
          label: '手动',
          value: 'manual'
        }
      ]
    },
    source_id: {
      label: '来源id',
      type: 'number',
    },
    key: {
      label: 'key', // 格式：学段id-学科id-版本id-bookid-id1-id2-name
      required: true,
      type: 'string',
    },
    period: {
      label: '学段',
      type: 'string',
      // model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      type: 'string',
      // model: 'subject',
      required: true,
    },
    press_version: {
      label: '教材版本',
      type: 'string',
      // model: 'press-version',
      required: true,
    },
    book: {
      label: '教材id',
      model: 'book',
      required: true,
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
