const _ = require('lodash')
const enums = require('../../common/enums');
const studyService = require('../../common/services/study');

module.exports = {
  add,
}

/**
 * 添加下载记录
 * @param user 用户信息
 * @param source 来源
 * @param data 下载资源数据
 * @returns {Promise<void>}
 */
async function add(user, source, data) {
  if (source === enums.DownloadResource.SYS_EXAMPAPER) {
    const studyInfo = await studyService.getPeriodSubjectByName(user, data.period, data.subject);
    data.period = studyInfo.period;
    data.subject = studyInfo.subject;
  }
  const fields = ['name', 'type', 'period', 'subject'];
  await strapi.query('download-record').create({
    ...(_.pick(data, fields)),
    source: source,
    source_id: data.id.toString(),
    url: data.url || '',
    suffix: data.suffix  || '',
    user: user.id,
    pBranch: user.pBranch.id
  });
}
