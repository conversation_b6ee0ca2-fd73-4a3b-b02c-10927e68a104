const axios = require('axios');
const URL = require('url');
const qs = require('querystring');
const server = strapi.config.server.seApi;
const logger = strapi.log;

module.exports = {
  search,
}

const options  = {
  timeout: 10 * 1000
}


async function search(params) {
  const url = URL.format({
    host: server.url,
    pathname: `/se_jzl/v2/search/question`,
  });
  try {
    const result = await axios.post(url, params, options);
    if (result.data.code !== 0) {
      logger.error(`试题搜索失败: ${JSON.stringify(result.data)}`);
    }
    return result.data.data;
  } catch (e) {
    logger.error(e);
    return null;
  }

}

