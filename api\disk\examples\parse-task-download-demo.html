<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解析任务图片批量下载ZIP - 测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .example-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .example-section h3 {
            margin-top: 0;
            color: #495057;
        }
        code {
            background-color: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 解析任务图片批量下载ZIP</h1>
        
        <div class="example-section">
            <h3>📋 功能说明</h3>
            <p>此功能用于批量下载解析任务中的图片文件，并打包成ZIP格式返回。</p>
            <ul>
                <li>✅ 只支持 <code>task_type</code> 为 <code>images</code> 的解析任务</li>
                <li>✅ 自动验证用户权限和任务有效性</li>
                <li>✅ 支持最多100个文件的批量下载</li>
                <li>✅ 包含下载结果摘要文件</li>
                <li>✅ 部分失败容错处理</li>
            </ul>
        </div>

        <form id="downloadForm">
            <div class="form-group">
                <label for="baseUrl">服务器地址:</label>
                <input type="text" id="baseUrl" value="http://localhost:1337" placeholder="http://localhost:1337">
            </div>

            <div class="form-group">
                <label for="token">JWT Token:</label>
                <textarea id="token" rows="3" placeholder="请输入您的JWT Token"></textarea>
            </div>

            <div class="form-group">
                <label for="taskId">解析任务ID:</label>
                <input type="text" id="taskId" placeholder="24位字符串，如: 507f1f77bcf86cd799439011">
            </div>

            <div class="form-group">
                <label for="zipName">ZIP文件名:</label>
                <input type="text" id="zipName" value="images.zip" placeholder="images.zip">
            </div>

            <div class="form-group">
                <label for="timeout">超时时间 (毫秒):</label>
                <input type="number" id="timeout" value="30000" min="1000" max="300000" placeholder="30000">
            </div>

            <button type="submit" id="downloadBtn">🚀 开始下载</button>
            <button type="button" id="clearLogBtn">🗑️ 清空日志</button>
        </form>

        <div id="progressContainer" style="display: none;">
            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>
            <div id="progressText">准备下载...</div>
        </div>

        <div id="messages"></div>

        <div class="log" id="logContainer">
            <div id="logContent">等待操作...</div>
        </div>

        <div class="example-section">
            <h3>🔧 API接口信息</h3>
            <p><strong>接口地址:</strong> <code>POST /parse-tasks/actions/downloadZip</code></p>
            <p><strong>请求参数:</strong></p>
            <pre>{
  "id": "解析任务ID (必填)",
  "zipName": "压缩包名称 (可选)",
  "timeout": "超时时间毫秒 (可选)"
}</pre>
            <p><strong>响应:</strong> 直接返回ZIP文件流</p>
        </div>
    </div>

    <script>
        const form = document.getElementById('downloadForm');
        const downloadBtn = document.getElementById('downloadBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const messagesContainer = document.getElementById('messages');
        const logContent = document.getElementById('logContent');

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logContent.textContent += logMessage + '\n';
            logContent.scrollTop = logContent.scrollHeight;
            
            console.log(logMessage);
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            messagesContainer.appendChild(messageDiv);
            
            // 5秒后自动移除消息
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }

        // 更新进度
        function updateProgress(percent, text) {
            progressBar.style.width = percent + '%';
            progressText.textContent = text;
        }

        // 清空日志
        clearLogBtn.addEventListener('click', () => {
            logContent.textContent = '日志已清空...\n';
            messagesContainer.innerHTML = '';
        });

        // 表单提交处理
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const baseUrl = document.getElementById('baseUrl').value.trim();
            const token = document.getElementById('token').value.trim();
            const taskId = document.getElementById('taskId').value.trim();
            const zipName = document.getElementById('zipName').value.trim() || 'images.zip';
            const timeout = parseInt(document.getElementById('timeout').value) || 30000;

            // 验证输入
            if (!baseUrl || !token || !taskId) {
                showMessage('请填写所有必填字段', 'error');
                return;
            }

            if (taskId.length !== 24) {
                showMessage('任务ID必须是24位字符串', 'error');
                return;
            }

            // 禁用按钮，显示进度
            downloadBtn.disabled = true;
            progressContainer.style.display = 'block';
            updateProgress(0, '开始下载...');

            log('开始批量下载ZIP...');
            log(`服务器: ${baseUrl}`);
            log(`任务ID: ${taskId}`);
            log(`文件名: ${zipName}`);
            log(`超时: ${timeout}ms`);

            try {
                updateProgress(20, '发送请求...');

                const response = await fetch(`${baseUrl}/parse-tasks/actions/downloadZip`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        id: taskId,
                        zipName: zipName,
                        timeout: timeout
                    })
                });

                updateProgress(50, '处理响应...');

                log(`响应状态: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
                }

                if (response.status === 200) {
                    log('✅ 状态码正确 (200)');
                } else {
                    log(`⚠️ 状态码异常: ${response.status}`);
                }

                updateProgress(70, '下载文件...');

                // 获取文件blob
                const blob = await response.blob();
                
                updateProgress(90, '保存文件...');

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = zipName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                updateProgress(100, '下载完成!');

                log(`✅ 下载成功: ${zipName}`);
                log(`文件大小: ${(blob.size / 1024 / 1024).toFixed(2)} MB`);
                showMessage(`下载成功: ${zipName} (${(blob.size / 1024 / 1024).toFixed(2)} MB)`, 'success');

            } catch (error) {
                updateProgress(0, '下载失败');
                log(`❌ 下载失败: ${error.message}`);
                showMessage(`下载失败: ${error.message}`, 'error');
            } finally {
                downloadBtn.disabled = false;
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 3000);
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，准备就绪');
            
            // 尝试从localStorage恢复配置
            const savedBaseUrl = localStorage.getItem('parseTaskDownload_baseUrl');
            const savedToken = localStorage.getItem('parseTaskDownload_token');
            
            if (savedBaseUrl) {
                document.getElementById('baseUrl').value = savedBaseUrl;
            }
            if (savedToken) {
                document.getElementById('token').value = savedToken;
            }
        });

        // 保存配置到localStorage
        document.getElementById('baseUrl').addEventListener('change', (e) => {
            localStorage.setItem('parseTaskDownload_baseUrl', e.target.value);
        });

        document.getElementById('token').addEventListener('change', (e) => {
            localStorage.setItem('parseTaskDownload_token', e.target.value);
        });
    </script>
</body>
</html>
