# 解析任务图片批量下载ZIP功能

## 功能概述

为解析任务（parse-task）添加批量下载图片文件并打包成ZIP的功能。该功能专门针对 `task_type` 为 `images` 的解析任务，可以将任务关联的所有图片文件下载并压缩成ZIP包返回给用户。

## API接口

### 批量下载图片ZIP

**接口地址**: `POST /parse-tasks/actions/downloadZip`

**请求参数**:
```json
{
  "id": "解析任务ID (24位字符串)",
  "zipName": "压缩包名称 (可选，默认: images.zip)",
  "timeout": "下载超时时间毫秒 (可选，默认: 30000，范围: 1000-300000)"
}
```

**响应**: 直接返回ZIP文件流，浏览器会自动下载

**状态码**: 200 OK

**响应头**:
```
Content-Type: application/zip
Content-Disposition: attachment; filename="images.zip"
```

> **注意**: 接口使用流式响应，确保返回正确的200状态码，避免404错误

## 功能特性

### 1. 安全验证
- ✅ 验证用户权限（只能下载自己创建的任务）
- ✅ 验证任务类型（只支持 `images` 类型）
- ✅ 验证任务存在性和租户隔离
- ✅ 验证文件有效性

### 2. 性能优化
- ✅ 并发下载限制（最多5个并发）
- ✅ 快速压缩（压缩级别1，适合图片文件）
- ✅ 流式处理（内存占用低）
- ✅ 超时控制（防止长时间等待）

### 3. 错误处理
- ✅ 部分失败容错（部分文件下载失败不影响其他文件）
- ✅ 详细错误记录
- ✅ 下载结果摘要

### 4. 文件管理
- ✅ 安全文件名生成（移除特殊字符）
- ✅ 文件名长度限制（最大200字符）
- ✅ 重复文件名处理
- ✅ 文件数量限制（最多100个文件）

## 使用示例

### JavaScript/Node.js 示例

```javascript
const axios = require('axios')
const fs = require('fs')

async function downloadTaskZip() {
  try {
    const response = await axios.post('/parse-tasks/actions/downloadZip', {
      id: '507f1f77bcf86cd799439011', // 解析任务ID
      zipName: 'my-images.zip',
      timeout: 60000 // 60秒超时
    }, {
      responseType: 'stream',
      headers: {
        'Authorization': 'Bearer your-jwt-token'
      }
    })

    // 保存ZIP文件
    const writer = fs.createWriteStream('./downloaded-images.zip')
    response.data.pipe(writer)

    writer.on('finish', () => {
      console.log('ZIP文件下载完成')
    })

    writer.on('error', (err) => {
      console.error('保存文件失败:', err)
    })

  } catch (error) {
    console.error('下载失败:', error.response?.data || error.message)
  }
}

downloadTaskZip()
```

### 浏览器 JavaScript 示例

```javascript
async function downloadTaskZip(taskId) {
  try {
    const response = await fetch('/parse-tasks/actions/downloadZip', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token')
      },
      body: JSON.stringify({
        id: taskId,
        zipName: 'task-images.zip'
      })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || '下载失败')
    }

    // 创建下载链接
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'task-images.zip'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)

    console.log('下载完成')
  } catch (error) {
    console.error('下载失败:', error.message)
    alert('下载失败: ' + error.message)
  }
}

// 使用示例
downloadTaskZip('507f1f77bcf86cd799439011')
```

### cURL 示例

```bash
curl -X POST \
  http://localhost:1337/parse-tasks/actions/downloadZip \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your-jwt-token' \
  -d '{
    "id": "507f1f77bcf86cd799439011",
    "zipName": "images.zip",
    "timeout": 30000
  }' \
  --output images.zip
```

## ZIP包内容

下载的ZIP包包含以下内容：

### 1. 图片文件
- 原始文件名（如果安全）
- 自动生成的安全文件名（如果原始文件名包含特殊字符）
- 保持原始文件格式

### 2. 下载摘要文件 (download_summary.json)
```json
{
  "taskId": "507f1f77bcf86cd799439011",
  "taskName": "数学试卷图片解析",
  "taskType": "images",
  "downloadTime": "2024-01-15T10:30:00.000Z",
  "totalFiles": 10,
  "successCount": 9,
  "failedCount": 1,
  "successFiles": [
    {
      "id": "507f1f77bcf86cd799439012",
      "name": "page1.jpg",
      "filename": "page1.jpg",
      "size": 245760,
      "url": "https://example.com/files/page1.jpg"
    }
  ],
  "failedFiles": [
    {
      "id": "507f1f77bcf86cd799439013",
      "name": "page2.jpg",
      "url": "https://example.com/files/page2.jpg",
      "error": "下载文件失败: 404 Not Found"
    }
  ]
}
```

## 错误处理

### 常见错误码

| 错误信息 | 原因 | 解决方案 |
|---------|------|----------|
| `参数错误` | 请求参数格式不正确 | 检查参数格式和类型 |
| `解析任务不存在或无权限访问` | 任务ID不存在或无权限 | 确认任务ID和用户权限 |
| `只有图片解析任务才支持批量下载ZIP` | 任务类型不是images | 只能对images类型任务使用此功能 |
| `未找到相关的图片文件` | 任务没有关联文件 | 确认任务已正确关联图片文件 |
| `没有有效的图片文件URL` | 文件URL为空 | 检查文件上传和URL生成 |
| `文件数量过多` | 超过100个文件限制 | 分批处理或联系管理员 |

### 部分失败处理

即使部分文件下载失败，系统仍会：
1. 继续下载其他文件
2. 生成包含成功文件的ZIP包
3. 在摘要文件中记录失败详情
4. 返回部分成功的结果

## 性能考虑

### 服务器资源消耗

| 文件总大小 | CPU消耗 | 内存消耗 | 处理时间 |
|-----------|---------|----------|----------|
| 10MB | 0.07核·秒 | ~100MB | 1-3秒 |
| 50MB | 0.35核·秒 | ~120MB | 5-10秒 |
| 100MB | 0.7核·秒 | ~150MB | 10-20秒 |
| 500MB | 3.5核·秒 | ~200MB | 30-60秒 |

### 并发限制建议

- **小文件(<50MB总量)**: 支持10-20并发用户
- **中等文件(50-200MB)**: 支持3-8并发用户  
- **大文件(>200MB)**: 建议限制为1-3并发用户

## 监控和日志

系统会记录以下信息：
- 下载开始和完成时间
- 成功/失败文件统计
- 错误详情和原因
- 用户操作记录

建议监控：
- 下载成功率
- 平均处理时间
- 服务器资源使用情况
- 错误频率和类型
