const settings = require('../../../plugins/users-permissions/models/user.settings.json')

module.exports = {
  ...settings,
  attributes: Object.assign({}, settings.attributes, {
    yjUserId: {
      label: '阅卷用户id',
      type: 'string',
      unique: true,
    },
    yjUserInfo: {
      label: '阅卷用户信息',
      type: 'json'
    },
    curPeriod: {
      label: '学段',
      type: 'string',
    },
    curSubject: {
      label: '学科',
      type: 'string',
    },
  })
}
