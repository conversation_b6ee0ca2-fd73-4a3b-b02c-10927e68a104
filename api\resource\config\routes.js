const { createDefaultRoutes } = require('accel-utils')
module.exports = {
  'routes': [
    // 备课资源
    ...createDefaultRoutes({
      basePath: '/resources',
      controller: 'resource'
    }),
    {
      'method': 'GET',
      'path': '/resources/actions/getList',
      'handler': 'resource.getList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/resources/actions/getUserList',
      'handler': 'resource.getUserList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/resources/actions/:id/getById',
      'handler': 'resource.getById',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/resources/actions/createResource',
      'handler': 'resource.createResource',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/resources/actions/:id/updateShareStatus',
      'handler': 'resource.updateShareStatus',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/resources/actions/:id/refToPerson',
      'handler': 'resource.refToPerson',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/resources/actions/:id/:action/updateTimes',
      'handler': 'resource.updateTimes',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/resources/actions/:id/deleteById',
      'handler': 'resource.deleteById',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/resources/actions/getAllRef',
      'handler': 'resource.getAllRef',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/resources/actions/:id/updateChapter',
      'handler': 'resource.updateChapter',
      'config': {
        'policies': [], 'prefix': '',
      }
    }
    // 结束
  ]
}
