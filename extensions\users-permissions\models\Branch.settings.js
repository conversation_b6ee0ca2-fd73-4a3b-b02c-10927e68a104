const settings = require('../../../plugins/users-permissions/models/branch.settings.json')

module.exports = {
    ...settings,
    attributes: Object.assign({}, settings.attributes, {
        "domain": {
            "label": "租户域名",
            "type": "string",
            "required": true,
            "maxLength": 64,
            // "unique": true,
            "minLength": 1
        },
        eduSystem: {
            label: '学制',
            type: 'number'
        },
        yjSchoolId: {
            label: '阅卷学校ID',
            type: 'number',
            required: true,
        },
        periods: {
            label: '学段',
            collection: 'period',
        },
        subjects: {
            label: '学科',
            collection: 'subject',
        },
    })
}
