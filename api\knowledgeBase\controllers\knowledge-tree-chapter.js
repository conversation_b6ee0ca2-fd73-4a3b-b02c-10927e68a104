'use strict'
const { BranchCurdRouter } = require('accel-utils')
const axios = require('axios')
const _ = require('lodash')
const { deleteLastLevelByIds } = require('../services/tree')

const branchCurdRouter = new (class extends BranchCurdRouter {
  async branchDelete (ctx) {
    const { params } = this._parseBranchCtx(ctx)
    const chapter = await strapi.query('knowledge-tree-chapter').findOne({ id: params.id, }, ['knowledge_tree'])
    if (!chapter || chapter.knowledge_tree.children.length === 0) {
      return ctx.wrapper.error('HANDLE_ERROR', 'chapter not found')
    }
    let paths = chapter.path.split('-').slice(4)
    deleteLastLevelByIds(chapter.knowledge_tree, paths)
    await strapi.query('knowledge-tree').update({ id: chapter.knowledge_tree.id, }, { children: chapter.knowledge_tree.children })
    return super.branchDelete(ctx)
  }

  async branchUpdate (ctx) {
    const { params, data } = this._parseBranchCtx(ctx)
    const userId = ctx.state.user.id
    if (data.name) {
      const chapter = await strapi.query('knowledge-tree-chapter').findOne({ id: params.id, }, [])
      data.key = chapter.path.replace(chapter.id, data.name)
    }
    data.operator = userId
    return super.branchUpdate(ctx)
  }
})('knowledge-tree-chapter')

module.exports = {
  ...branchCurdRouter.createHandlers(),
}