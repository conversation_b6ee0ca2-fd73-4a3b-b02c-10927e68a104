module.exports = {
  collectionName: 'knowledge',
  info: {
    name: 'knowledge',
    label: '知识点',
    description: '知识点'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { key: 1 }, options: { unique: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '知识点名称',
      type: 'string',
      required: true,
    },
    abstract: {
      label: '摘要',
      type: 'string',
    },
    contents: {
      label: '内容',
      type: 'json'
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'manual',
      options: [
        {
          label: '同步',
          value: 'sync'
        },
        {
          label: '手动',
          value: 'manual'
        }
      ]
    },
    source_id: {
      label: '来源id',
      type: 'number',
    },
    key: {
      label: 'key', // name-period-subject-pBranch
      required: true,
      type: 'string',
    },
    period: {
      label: '学段',
      type: 'string',
      // model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      type: 'string',
      // model: 'subject',
      required: true,
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
