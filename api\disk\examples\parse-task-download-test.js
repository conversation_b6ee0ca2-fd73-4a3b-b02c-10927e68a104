/**
 * 解析任务图片批量下载ZIP功能测试示例
 */

const axios = require('axios')
const fs = require('fs')
const path = require('path')

// 配置
const config = {
  baseURL: 'http://localhost:1337',
  token: 'your-jwt-token-here', // 替换为实际的JWT token
  taskId: '507f1f77bcf86cd799439011' // 替换为实际的解析任务ID
}

/**
 * 测试批量下载ZIP功能
 */
async function testDownloadTaskImagesZip() {
  console.log('🚀 开始测试解析任务图片批量下载ZIP功能')
  console.log('=' * 60)

  try {
    // 测试参数
    const testCases = [
      {
        name: '基本下载测试',
        params: {
          id: config.taskId,
          zipName: 'test-images.zip',
          timeout: 30000
        }
      },
      {
        name: '自定义文件名测试',
        params: {
          id: config.taskId,
          zipName: '数学试卷图片_' + new Date().toISOString().slice(0, 10) + '.zip',
          timeout: 60000
        }
      },
      {
        name: '快速超时测试',
        params: {
          id: config.taskId,
          zipName: 'quick-test.zip',
          timeout: 5000 // 5秒超时，用于测试超时处理
        }
      }
    ]

    for (const testCase of testCases) {
      console.log(`\n📊 ${testCase.name}`)
      console.log('-'.repeat(40))

      try {
        const startTime = Date.now()

        const response = await axios.post(
          `${config.baseURL}/parse-tasks/actions/downloadImagesZip`,
          testCase.params,
          {
            responseType: 'stream',
            headers: {
              'Authorization': `Bearer ${config.token}`,
              'Content-Type': 'application/json'
            },
            timeout: testCase.params.timeout + 5000 // 给axios额外5秒缓冲
          }
        )

        // 保存文件
        const outputPath = path.join(__dirname, 'downloads', testCase.params.zipName)
        
        // 确保下载目录存在
        const downloadDir = path.dirname(outputPath)
        if (!fs.existsSync(downloadDir)) {
          fs.mkdirSync(downloadDir, { recursive: true })
        }

        const writer = fs.createWriteStream(outputPath)
        response.data.pipe(writer)

        await new Promise((resolve, reject) => {
          writer.on('finish', resolve)
          writer.on('error', reject)
        })

        const endTime = Date.now()
        const fileStats = fs.statSync(outputPath)

        console.log(`✅ 下载成功`)
        console.log(`   文件路径: ${outputPath}`)
        console.log(`   文件大小: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`)
        console.log(`   下载时间: ${endTime - startTime} ms`)
        console.log(`   响应头: ${JSON.stringify(response.headers['content-disposition'])}`)

      } catch (error) {
        console.log(`❌ 下载失败`)
        
        if (error.response) {
          console.log(`   HTTP状态: ${error.response.status}`)
          console.log(`   错误信息: ${JSON.stringify(error.response.data)}`)
        } else if (error.code === 'ECONNABORTED') {
          console.log(`   错误类型: 请求超时`)
          console.log(`   超时时间: ${testCase.params.timeout}ms`)
        } else {
          console.log(`   错误信息: ${error.message}`)
        }
      }
    }

  } catch (error) {
    console.error('测试执行失败:', error.message)
  }

  console.log('\n🎉 测试完成!')
}

/**
 * 测试错误情况
 */
async function testErrorCases() {
  console.log('\n🔍 开始测试错误情况')
  console.log('=' * 60)

  const errorTestCases = [
    {
      name: '无效任务ID',
      params: {
        id: '000000000000000000000000', // 不存在的ID
        zipName: 'error-test.zip'
      },
      expectedError: '解析任务不存在或无权限访问'
    },
    {
      name: '参数格式错误',
      params: {
        id: 'invalid-id', // 格式错误的ID
        zipName: 'error-test.zip'
      },
      expectedError: '参数错误'
    },
    {
      name: '超长超时时间',
      params: {
        id: config.taskId,
        zipName: 'error-test.zip',
        timeout: 500000 // 超过最大限制
      },
      expectedError: '参数错误'
    }
  ]

  for (const testCase of errorTestCases) {
    console.log(`\n📊 ${testCase.name}`)
    console.log('-'.repeat(40))

    try {
      const response = await axios.post(
        `${config.baseURL}/parse-tasks/actions/downloadImagesZip`,
        testCase.params,
        {
          headers: {
            'Authorization': `Bearer ${config.token}`,
            'Content-Type': 'application/json'
          }
        }
      )

      console.log(`❌ 预期失败但成功了`)
      console.log(`   响应: ${JSON.stringify(response.data)}`)

    } catch (error) {
      if (error.response && error.response.data) {
        const errorMessage = error.response.data.message || error.response.data.error
        console.log(`✅ 正确返回错误`)
        console.log(`   错误信息: ${errorMessage}`)
        console.log(`   HTTP状态: ${error.response.status}`)
        
        if (errorMessage.includes(testCase.expectedError)) {
          console.log(`   ✓ 错误信息符合预期`)
        } else {
          console.log(`   ⚠️ 错误信息不符合预期，期望包含: ${testCase.expectedError}`)
        }
      } else {
        console.log(`❌ 网络错误: ${error.message}`)
      }
    }
  }
}

/**
 * 性能测试
 */
async function performanceTest() {
  console.log('\n⚡ 开始性能测试')
  console.log('=' * 60)

  const concurrentTests = [1, 2, 3] // 并发数测试

  for (const concurrency of concurrentTests) {
    console.log(`\n📊 并发数: ${concurrency}`)
    console.log('-'.repeat(40))

    const promises = []
    const startTime = Date.now()

    for (let i = 0; i < concurrency; i++) {
      const promise = axios.post(
        `${config.baseURL}/parse-tasks/actions/downloadImagesZip`,
        {
          id: config.taskId,
          zipName: `perf-test-${i}.zip`,
          timeout: 60000
        },
        {
          responseType: 'stream',
          headers: {
            'Authorization': `Bearer ${config.token}`,
            'Content-Type': 'application/json'
          }
        }
      ).then(response => {
        return new Promise((resolve, reject) => {
          let totalSize = 0
          response.data.on('data', chunk => {
            totalSize += chunk.length
          })
          response.data.on('end', () => {
            resolve({ success: true, size: totalSize })
          })
          response.data.on('error', reject)
        })
      }).catch(error => {
        return { success: false, error: error.message }
      })

      promises.push(promise)
    }

    try {
      const results = await Promise.all(promises)
      const endTime = Date.now()

      const successCount = results.filter(r => r.success).length
      const totalSize = results.reduce((sum, r) => sum + (r.size || 0), 0)

      console.log(`   成功数: ${successCount}/${concurrency}`)
      console.log(`   总时间: ${endTime - startTime} ms`)
      console.log(`   总大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`)
      console.log(`   平均速度: ${((totalSize / 1024 / 1024) / ((endTime - startTime) / 1000)).toFixed(2)} MB/s`)

      results.forEach((result, index) => {
        if (!result.success) {
          console.log(`   请求${index + 1}失败: ${result.error}`)
        }
      })

    } catch (error) {
      console.log(`❌ 并发测试失败: ${error.message}`)
    }
  }
}

/**
 * 主测试函数
 */
async function runAllTests() {
  console.log('🧪 解析任务图片批量下载ZIP功能测试套件')
  console.log('=' * 80)
  console.log(`配置信息:`)
  console.log(`  服务器: ${config.baseURL}`)
  console.log(`  任务ID: ${config.taskId}`)
  console.log(`  Token: ${config.token.substring(0, 20)}...`)
  console.log('=' * 80)

  // 基本功能测试
  await testDownloadTaskImagesZip()

  // 错误情况测试
  await testErrorCases()

  // 性能测试
  await performanceTest()

  console.log('\n🏁 所有测试完成!')
  console.log('\n💡 使用说明:')
  console.log('1. 请确保服务器正在运行')
  console.log('2. 替换config中的token和taskId为实际值')
  console.log('3. 确保测试的解析任务类型为"images"')
  console.log('4. 下载的文件保存在 ./downloads/ 目录中')
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = {
  testDownloadTaskImagesZip,
  testErrorCases,
  performanceTest,
  runAllTests
}
