const { CurdRouter } = require('accel-utils');
const _ = require('lodash');
const moment = require('moment');
const enums = require('../../common/enums');
const downloadRecordService = require('../../user/services/download-record');
const utils = require('../../common/lib/utils');
const MODEL = 'resource';
const curdRouter = new CurdRouter(MODEL);


module.exports = {
  ...curdRouter.createHandlers(),
  getList,
  getUserList,
  createResource,
  updateShareStatus,
  refToPerson,
  updateTimes,
  deleteById,
  getAllRef,
  updateChapter,
  getById,
}

async function getList(ctx) {
  const user = ctx.state.user;
  const { period, subject, type = '', source, name, book, book_chapter, sort_by = 'time', offset = 0, limit = 10} = ctx.request.query;
  // const studyInfo = await studyService.getPeriodSubjectByName(user, period, subject);
  const query = {
    period,
    subject,
    shared: enums.Bool.YES,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    _start: offset,
    _limit: limit,
    _sort: 'createdAt:DESC',
  }
  if (name) query.name_contains = name;
  if (type) query.type = type;
  if (source) query.source = source;
  if (book) query.book = book;
  if (book_chapter) {
    query.book_chapter_in = await getAllChildren(book_chapter);
  }
  if (sort_by === 'view_times') query._sort = 'view_times:DESC';
  const result = {
    total: 0,
    list: []
  };
  result.total = await strapi.query(MODEL).count(query);
  if (!result.total) return ctx.wrapper.succ(result);

  result.list = await strapi.query(MODEL).find(query);

  for (const data of result.list) {
    handleData(data);
  }
  return ctx.wrapper.succ(result);
}

async function getUserList(ctx) {
  const user = ctx.state.user;
  const { period, subject, type = '', source, name, book, book_chapter, sort_by = 'time', time = 0, offset = 0, limit = 10} = ctx.request.query;
  const query = {
    period,
    subject,
    user: user.id,
    deleted: enums.Bool.NO,
    _start: offset,
    _limit: limit,
    _sort: 'createdAt:DESC'
  }
  if (name) query.name_contains = name;
  if (type) query.type = type;

  if (source) query.source = source;
  if (time) query.createdAt_gte = moment(Number(time)).startOf('day').toDate();
  if (book) query.book = book;
  if (book_chapter) query.book_chapter_in = await getAllChildren(book_chapter);
  if (sort_by === 'view_times') query._sort = 'view_times:DESC';
  const result = {
    total: 0,
    list: []
  };
  result.total = await strapi.query(MODEL).count(query);
  if (!result.total) return ctx.wrapper.succ(result);

  result.list = await strapi.query(MODEL).find(query);
  for (const data of result.list) {
    handleData(data);
  }
  return ctx.wrapper.succ(result);
}

async function getAllChildren(book_chapter) {
  const chapter = await strapi.query('book-chapter').findOne({id: book_chapter});
  const book_chapters = [];
  if (!_.isEmpty(chapter)) {
    const book = chapter.book;
    const fun = (children, is_child = false) => {
      if (!children || !_.size(children)) return;
      for (const child of children) {
        if (child.id === book_chapter || is_child) {
          book_chapters.push(child.id);
          fun(child.children, true);
        } else {
          fun(child.children, is_child);
        }
      }
    }
    fun(book.children);
  } else {
    book_chapters.push(book_chapter);
  }
  return book_chapters;
}

function handleData(data) {
  data.press_version = utils.pickFields(data.press_version, ['id', 'name']);
  data.book = utils.pickFields(data.book, ['id', 'name']);
  data.book_chapter = utils.pickFields(data.book_chapter, ['id', 'name']);
  data.user = utils.pickFields(data.user, ['id', 'username']);
  data.period = utils.pickFields(data.period, ['id', 'name']);
  data.subject = utils.pickFields(data.subject, ['id', 'name']);
  delete data.pBranch;
  delete data.deleted;
  // delete data.user;
}

async function getById(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  const data = await strapi.query(MODEL).findOne({id, pBranch: user.pBranch.id});
  if (_.isEmpty(data)) return ctx.wrapper.succ({});
  return ctx.wrapper.succ(data);
}

async function createResource(ctx) {
  const user = ctx.state.user;
  const params = ctx.request.body;
  const { period, subject, shared, list} = params;
  if (!_.size(list)) return ctx.wrapper.error('HANDLE_ERROR', '文件不能为空');
  const ids = [];
  for (const item of list) {
    item.period = period;
    item.subject = subject;
    item.shared = shared;
    item.user = user.id;
    item.pBranch = user.pBranch.id;
    const doc = await strapi.query(MODEL).create(item);
    ids.push(doc.id);
  }
  return ctx.wrapper.succ({ids});
}

// 分享取消分享
async function updateShareStatus(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  const data = await strapi.query(MODEL).findOne({id, user: user.id});
  if (_.isEmpty(data)) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  await strapi.query(MODEL).update({id}, {shared: data.shared ? enums.Bool.NO : enums.Bool.YES});
  return ctx.wrapper.succ({id});
}

// 添加到个人空间
async function refToPerson(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  const data = await strapi.query(MODEL).findOne({id, shared: enums.Bool.YES, pBranch: user.pBranch.id, deleted: enums.Bool.NO});
  if (_.isEmpty(data) || data.user.id === user.id) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  const newData = _.pick(data, [
    'name', 'type', 'period', 'subject',
    'press_version', 'book', 'book_chapter', 'url', 'size', 'suffix'
  ]);
  newData.source = enums.ResourceFrom.REF;
  newData.source_id = data.id;
  newData.user = user.id;
  newData.pBranch = user.pBranch.id;
  const doc = await strapi.query(MODEL).create(newData);
  return ctx.wrapper.succ({id: doc.id});
}

// 更新浏览或者下载次数
async function updateTimes(ctx) {
  const user = ctx.state.user;
  const {id, action} = ctx.params;

  const data = await strapi.query(MODEL).findOne({id, pBranch: user.pBranch.id, deleted: enums.Bool.NO});
  if (_.isEmpty(data)) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  const update = {};
  if (action === 'view_times') {
    update.view_times = data.view_times + 1;
    update.last_view_time = new Date();
  } else {
    update.download_times = data.download_times + 1;
    update.last_download_time = new Date();
    await downloadRecordService.add(user, enums.DownloadResource.RESOURCE, data);
  }
  await strapi.query(MODEL).update({id}, update);
  return ctx.wrapper.succ({});
}

// 删除
async function deleteById(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  const data = await strapi.query(MODEL).findOne({id, pBranch: user.pBranch.id, deleted: enums.Bool.NO});
  if (_.isEmpty(data)) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  if (data.user.id !== user.id) { // 权限校验 超级管理员、年级主任、校领导
    const userRoles = user?.yjUserInfo?.schoolRoleType || [];
    if (!_.size(userRoles) || _.difference(enums.AdminRoles, userRoles).length === enums.AdminRoles.length)
      return ctx.wrapper.error('HANDLE_ERROR', '暂无权限');
  }
  await strapi.query(MODEL).update({id}, {deleted: enums.Bool.YES});
  return ctx.wrapper.succ({id});
}

// 获取所有的引用
async function getAllRef(ctx) {
  const user = ctx.state.user;
  const {period, subject} = ctx.request.query;
  const list = await strapi.query(MODEL, []).find({user: user.id, period, subject, source: enums.ResourceFrom.REF, deleted: enums.Bool.NO});
  const ids = list.map(e => e.source_id);
  return ctx.wrapper.succ(ids);
}

async function updateChapter(ctx) {
  const user = ctx.state.user;
  const {id} = ctx.params;
  // const {press_version, press_volume, book, book_chapter}
  const data = await strapi.query(MODEL).findOne({id, pBranch: user.pBranch.id, deleted: enums.Bool.NO});
  if (_.isEmpty(data)) return ctx.wrapper.error('HANDLE_ERROR', '数据不存在');
  if (data.user.id !== user.id) { // 权限校验 超级管理员、年级主任、校领导
    const userRoles = user?.yjUserInfo?.schoolRoleType || [];
    if (!_.size(userRoles) || _.difference(enums.AdminRoles, userRoles).length === enums.AdminRoles.length)
      return ctx.wrapper.error('HANDLE_ERROR', '暂无权限');
  }
  const doc = _.pick(ctx.request.body, ['press_version', 'press_volume', 'book', 'book_chapter']);
  await strapi.query(MODEL).update({id}, doc);
  return ctx.wrapper.succ({id});
}



