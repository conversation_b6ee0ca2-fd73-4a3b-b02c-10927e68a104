const { createDefaultRoutes } = require('accel-utils')
module.exports = {
  'routes': [
    ...createDefaultRoutes({ // 网盘文件
      basePath: '/disk-files',
      controller: 'disk-file'
    }),
    {
      'method': 'POST',
      'path': '/disk-files/actions/search',
      'handler': 'disk-file.search',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/disk-files/actions/getFolder',
      'handler': 'disk-file.getFolder',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/disk-files/actions/getDetails',
      'handler': 'disk-file.getDetails',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/disk-files/actions/addFile',
      'handler': 'disk-file.addFile',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/disk-files/actions/batchAddFile',
      'handler': 'disk-file.batchAddFile',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/disk-files/actions/updateFile',
      'handler': 'disk-file.updateFile',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/disk-files/actions/deleteFile',
      'handler': 'disk-file.deleteFile',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/disk-files/actions/:id/updateTopStatus',
      'handler': 'disk-file.updateTopStatus',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/disk-files/actions/batchShare',
      'handler': 'disk-file.batchShare',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/disk-files/actions/cancelShare',
      'handler': 'disk-file.cancelShare',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/disk-files/actions/updateFolder',
      'handler': 'disk-file.updateFolder',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/disk-files/actions/:id/download',
      'handler': 'disk-file.download',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/disk-files/actions/getShareUsers',
      'handler': 'disk-file.getShareUsers',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/disk-files/actions/searchShareUserFiles',
      'handler': 'disk-file.searchShareUserFiles',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/disk-files/actions/ref',
      'handler': 'disk-file.ref',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({ // 网盘文件日志
      basePath: '/disk-file-logs',
      controller: 'disk-file-log'
    }),
    {
      'method': 'GET',
      'path': '/disk-file-logs/actions/getList',
      'handler': 'disk-file-log.getList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/disk-file-logs/actions/:id/deleteById',
      'handler': 'disk-file-log.deleteById',
      'config': {
        'policies': [], 'prefix': '',
      }
    },

    ...createDefaultRoutes({ // 文件解析任务
      basePath: '/parse-tasks',
      controller: 'parse-task'
    }),
    {
      'method': 'POST',
      'path': '/parse-tasks/actions/createTask',
      'handler': 'parse-task.createTask',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/parse-tasks/actions/downloadZip',
      'handler': 'parse-task.downloadTaskZip',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
  ]
}
