'use strict'
const fs = require('fs')
const { writeFile } = require('fs/promises')
const axios = require('axios')
const archiver = require('archiver')
const path = require('path')
const { getSecurityToken, uploadFileToBucket } = require('../services/object-storage')
const { objectStorageConfig } = require('../config/object-storage')

/**
 * 将 Stream 转为 Buffer
 * @param stream
 * @returns {Promise<Buffer>}
 */
function stream2buffer (stream) {
  return new Promise((resolve, reject) => {
    const _buf = []
    stream.on('data', (chunk) => _buf.push(chunk))
    stream.on('end', () => resolve(Buffer.concat(_buf)))
    stream.on('error', (err) => reject(err))
  })
}

// 浏览器直接对接对象存储上传获取临时Token
async function getSts () {
  return await getSecurityToken()
}

// 对接对象存储的普通文件上传接口
async function upload (ctx) {
  const { files } = ctx.request
  const file = files.file
  const { name } = file
  const dir = [objectStorageConfig.baseDir , objectStorageConfig.uploadPath].filter(e => e).join('')
  let uploadInfo = await uploadFileToBucket(name, file.path, dir)
  return ctx.wrapper({ url: uploadInfo.url })
}

// 对接 WangEditor 的富文本文件上传接口
async function richTextUpload (ctx) {
  const { files } = ctx.request
  const file = files['wangeditor-uploaded-image']
  const { name } = file
  try {
    const dir = [objectStorageConfig.baseDir, objectStorageConfig.richTextUploadPath].filter(e => e).join('')
    let uploadInfo = await uploadFileToBucket(name, file.path, dir)
    return {
      errno: 0,
      data: {
        src: uploadInfo.url,
        url: uploadInfo.url,
      }
    }
  } catch (e) {
    console.error(e)
    return {
      errno: 1,
      message: e.toString()
    }
  }
}

// 上传到本地
async function uploadToLocal (ctx) {
  const { files } = ctx.request
  const file = files.file
  const fileHashName = file.path.match(/[^\\\/]+$/)[0]
  const fileName = `${fileHashName}_${file.name}`
  const buffer = await stream2buffer(fs.createReadStream(file.path))
  await writeFile(`./public/uploads/${fileName}`, buffer)
  const baseUrl = strapi.config.server.serverUrl
  const url = `${baseUrl}/uploads/${fileName}`
  return ctx.wrapper({ url })
}

// 富文本文件上传到本地
async function richTextUploadToLocal (ctx) {
  const { files } = ctx.request
  const file = files['wangeditor-uploaded-image']
  try {
    const fileHashName = file.path.match(/[^\\\/]+$/)[0]
    const fileName = `${fileHashName}_${file.name}`
    const buffer = await stream2buffer(fs.createReadStream(file.path))
    await writeFile(`./public/uploads/rich-text/${fileName}`, buffer)
    const baseUrl = strapi.config.server.serverUrl
    const url = `${baseUrl}/uploads/rich-text/${fileName}`
    return {
      errno: 0,
      data: {
        src: url,
        url: url,
      }
    }
  } catch (e) {
    return {
      errno: 1,
      message: e.toString()
    }
  }
}

/**
 * 从URL下载文件
 * @param {string} url - 文件URL
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<Buffer>} 文件Buffer
 */
async function downloadFileFromUrl(url, timeout = 30000) {
  try {
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      timeout: timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    return Buffer.from(response.data)
  } catch (error) {
    throw new Error(`下载文件失败: ${url} - ${error.message}`)
  }
}

/**
 * 从文件名中提取扩展名
 * @param {string} filename - 文件名
 * @returns {string} 扩展名
 */
function getFileExtension(filename) {
  return path.extname(filename).toLowerCase() || '.bin'
}

/**
 * 生成安全的文件名
 * @param {string} filename - 原始文件名
 * @param {number} index - 文件索引
 * @returns {string} 安全的文件名
 */
function generateSafeFilename(filename, index) {
  if (!filename) {
    return `file_${index}.bin`
  }

  // 移除不安全的字符
  const safeName = filename.replace(/[<>:"/\\|?*]/g, '_')

  // 如果文件名太长，截断它
  if (safeName.length > 200) {
    const ext = getFileExtension(safeName)
    const nameWithoutExt = safeName.substring(0, 200 - ext.length)
    return `${nameWithoutExt}${ext}`
  }

  return safeName
}

/**
 * 批量下载文件并打包成ZIP
 * @param {Object} ctx - Koa上下文
 */
async function batchDownloadAndZip(ctx) {
  try {
    const { urls, zipName = 'files.zip', timeout = 30000 } = ctx.request.body

    // 验证输入参数
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return ctx.wrapper.error('HANDLE_ERROR', '请提供有效的URL数组')
    }

    if (urls.length > 100) {
      return ctx.wrapper.error('HANDLE_ERROR', '一次最多只能下载100个文件')
    }

    // 创建ZIP压缩器
    const archive = archiver('zip', {
      zlib: { level: 9 } // 最高压缩级别
    })

    // 设置响应头
    ctx.set('Content-Type', 'application/zip')
    ctx.set('Content-Disposition', `attachment; filename="${encodeURIComponent(zipName)}"`)

    // 将ZIP流直接写入响应
    ctx.body = archive

    // 处理压缩器错误
    archive.on('error', (err) => {
      console.error('ZIP压缩错误:', err)
      if (!ctx.headerSent) {
        ctx.status = 500
        ctx.body = { error: '压缩文件时发生错误' }
      }
    })

    // 记录成功和失败的文件
    const results = {
      success: [],
      failed: []
    }

    // 并发下载文件（限制并发数为5）
    const concurrencyLimit = 5
    const downloadPromises = []

    for (let i = 0; i < urls.length; i += concurrencyLimit) {
      const batch = urls.slice(i, i + concurrencyLimit)
      const batchPromises = batch.map(async (urlInfo, batchIndex) => {
        const actualIndex = i + batchIndex

        try {
          // 支持字符串URL或对象格式 {url: string, filename?: string}
          const fileUrl = typeof urlInfo === 'string' ? urlInfo : urlInfo.url
          const customFilename = typeof urlInfo === 'object' ? urlInfo.filename : null

          if (!fileUrl) {
            throw new Error('URL不能为空')
          }

          console.log(`开始下载文件 ${actualIndex + 1}/${urls.length}: ${fileUrl}`)

          // 下载文件
          const fileBuffer = await downloadFileFromUrl(fileUrl, timeout)

          // 生成文件名
          let filename = customFilename
          if (!filename) {
            // 尝试从URL中提取文件名
            const urlPath = new URL(fileUrl).pathname
            filename = path.basename(urlPath) || `file_${actualIndex + 1}`
          }

          const safeFilename = generateSafeFilename(filename, actualIndex + 1)

          // 添加文件到ZIP
          archive.append(fileBuffer, { name: safeFilename })

          results.success.push({
            url: fileUrl,
            filename: safeFilename,
            size: fileBuffer.length
          })

          console.log(`文件下载成功: ${safeFilename} (${fileBuffer.length} bytes)`)

        } catch (error) {
          console.error(`文件下载失败 ${actualIndex + 1}:`, error.message)
          results.failed.push({
            url: typeof urlInfo === 'string' ? urlInfo : urlInfo.url,
            error: error.message
          })
        }
      })

      downloadPromises.push(...batchPromises)

      // 等待当前批次完成再处理下一批次
      await Promise.all(batchPromises)
    }

    // 等待所有下载完成
    await Promise.all(downloadPromises)

    // 如果没有成功下载任何文件
    if (results.success.length === 0) {
      if (!ctx.headerSent) {
        return ctx.wrapper.error('HANDLE_ERROR', '没有成功下载任何文件')
      }
      return
    }

    // 添加下载结果摘要文件
    const summary = {
      downloadTime: new Date().toISOString(),
      totalFiles: urls.length,
      successCount: results.success.length,
      failedCount: results.failed.length,
      successFiles: results.success,
      failedFiles: results.failed
    }

    archive.append(JSON.stringify(summary, null, 2), { name: 'download_summary.json' })

    console.log(`批量下载完成: 成功 ${results.success.length}/${urls.length} 个文件`)

    // 完成压缩
    archive.finalize()

  } catch (error) {
    console.error('批量下载ZIP错误:', error)
    if (!ctx.headerSent) {
      return ctx.wrapper.error('HANDLE_ERROR', `批量下载失败: ${error.message}`)
    }
  }
}

module.exports = {
  getSts,
  upload,
  richTextUpload,
  uploadToLocal,
  richTextUploadToLocal,
  batchDownloadAndZip,
}


