module.exports = {
  collectionName: 'textbook',
  info: {
    name: 'textbook',
    label: '教辅',
    description: '教辅资源管理'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { name: 1, pBranch: 1 }, options: {} },
      { keys: { period: 1, subject: 1, grade: 1 }, options: {} },
      { keys: { creator: 1 }, options: {} },
      { keys: { status: 1 }, options: {} },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '教辅名称',
      type: 'string',
      required: true,
      maxLength: 200,
    },
    description: {
      label: '教辅描述',
      type: 'text',
      default: '',
    },
    grade: {
      label: '年级',
      type: 'string',
      required: true,
    },
    period: {
      label: '学段',
      type: 'string',
      required: true,
      options: [
        { label: '小学', value: '小学' },
        { label: '初中', value: '初中' },
        { label: '高中', value: '高中' },
      ]
    },
    subject: {
      label: '科目',
      type: 'string',
      required: true,
      options: [
        { label: '语文', value: '语文' },
        { label: '数学', value: '数学' },
        { label: '英语', value: '英语' },
        { label: '物理', value: '物理' },
        { label: '化学', value: '化学' },
        { label: '生物', value: '生物' },
        { label: '地理', value: '地理' },
        { label: '历史', value: '历史' },
        { label: '政治', value: '政治' },
        { label: '道德与法治', value: '道德与法治' },
        { label: '科学', value: '科学' },
        { label: '信息技术', value: '信息技术' },
        { label: '音乐', value: '音乐' },
        { label: '美术', value: '美术' },
        { label: '体育', value: '体育' },
      ]
    },
    cover_url: {
      label: '封面URL',
      type: 'string',
      default: '',
    },
    cover_file: {
      label: '封面文件',
      model: 'disk-file',
    },
    disk_file: {
      label: '网盘文件',
      model: 'disk-file',
      via: 'textbook',
    },
    tags: {
      label: '标签',
      type: 'json',
      default: [],
      jsonSchema: {
        type: 'array',
        items: {
          type: 'string'
        }
      }
    },
    view_times: {
      label: '浏览次数',
      type: 'number',
      default: 0,
    },
    download_times: {
      label: '下载次数',
      type: 'number',
      default: 0,
    },
    remark: {
      label: '备注',
      type: 'text',
      default: '',
    },
    user_id: {
      label: '用户ID',
      type: 'string',
      required: true,
    },
    school_id: {
      label: '学校ID',
      type: 'number',
      required: true
    },
    deleted: {
      label: '删除标识',
      type: 'number',
      default: 0,
      options: [
        { label: '正常', value: 0 },
        { label: '已删除', value: 1 },
      ]
    }
  }
}
