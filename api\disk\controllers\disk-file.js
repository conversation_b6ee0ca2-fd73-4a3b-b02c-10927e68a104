const { <PERSON>urdRouter } = require('accel-utils');
const _ = require('lodash');
const Joi = require('joi');
const { ObjectId } = require('mongodb');
const client = require('../../common/client');
const utils = require('../../common/lib/utils');
const enums = require('../../common/enums');
const path = require('path');
const curdRouter = new CurdRouter(enums.CollectionName.DISK_FILE);

const ROOT_FOLDER = '0';

module.exports = {
  ...curdRouter.createHandlers(),
  search,
  getFolder,
  getDetails,
  addFile,
  batchAddFile,
  updateFile,
  deleteFile,
  updateTopStatus,
  batchShare,
  cancelShare,
  updateFolder,
  download,
  getShareUsers,
  searchShareUserFiles,
  ref,
}

const JOI_SEARCH = Joi.object({
  offset: Joi.number().integer().required(),
  limit: Joi.number().integer().min(1).required(),
  period: Joi.string().required(),
  subject: Joi.string().required(),
  name: Joi.string().optional().allow(''),
  // category: Joi.string().optional().allow(''),
  category: Joi.array().items(Joi.string()).optional(),
  suffixes: Joi.array().items(Joi.string()).optional(),
  source: Joi.array().items(Joi.string()).optional(), //
  parent_id: Joi.string().optional().allow('')
});

async function search(ctx) {
  const {error, value} = JOI_SEARCH.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const {name, suffixes, category, source} = value;
  const query = {
    period: value.period,
    subject: value.subject,
    parent_id: value.parent_id || '0',
    deleted: enums.Bool.NO,
    creator: user.id,
    _sort: 'is_top:DESC,updatedAt:DESC',
    _start: value.offset,
    _limit: value.limit
  };

  if (name) query['name_contains'] = name;
  if (_.size(suffixes)) query['suffix_in'] = suffixes;
  if (_.size(category)) query.category_in = category;
  if (source) query.source_in = source;

  const result = {
    total: 0,
    list: []
  };
  result.total = await strapi.query(enums.CollectionName.DISK_FILE).count(query);
  if (!result.total) return ctx.wrapper.succ(result);
  result.list = await strapi.query(enums.CollectionName.DISK_FILE).find(query);
  for (const data of result.list) {
    handleFileFields(data);
  }
  return ctx.wrapper.succ(result);
}

const JOI_GET_FOLDER = Joi.object({
  period: Joi.string().required(),
  subject: Joi.string().required(),
});

async function getFolder(ctx) {
  const {error, value} = JOI_GET_FOLDER.validate(ctx.request.query);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const {period, subject} = value;
  const list = await strapi.query(enums.CollectionName.DISK_FILE).find({
    period,
    subject,
    is_folder: enums.Bool.YES,
    creator: user.id,
    deleted: enums.Bool.NO,
    _sort: 'updatedAt:DESC',
    _projection: {id: 1, name: 1, parent_id: 1}
  });
  const result = {
    id: '0',
    name: '我的'
  }
  if (_.size(list)) {
    list.forEach(e => {
      delete e._id;
      delete e.creator;
      delete e.operator;
      delete e.pBranch;
    });
  }
  const tree = buildTree(list);
  if (_.size(tree)) result.children = tree;
  return ctx.wrapper.succ(result);
}

function buildTree(items, parent_id = '0') {
  const tree = [];
  for (const item of items) {
    if (item.parent_id === parent_id) {
      const children = buildTree(items, item.id);
      const newItem = _.assign({}, item);
      if (children.length > 0) {
        newItem.children = children;
      }
      tree.push(newItem);
    }
  }
  return tree;
}

const JOI_GET_DETAIL = Joi.object({
  id: Joi.string().required().length(24),
  children: Joi.number().optional().valid(0, 1).default(0),
});

async function getDetails(ctx) {
  const {error, value} = JOI_GET_DETAIL.validate(ctx.request.query);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const {id, children} = value;
  const file = await strapi.query(enums.CollectionName.DISK_FILE).findOne({id});
  if (_.isEmpty(file) || file.pBranch.id !== user.pBranch.id || file.deleted) return ctx.wrapper.error('HANDLE_ERROR', `数据不存在`);
  const share = !!user.id === file.creator.id;
  const result = {
    file: handleFileFields(file)
  }
  if (file.is_folder && children) {
    let list = await strapi.query(enums.CollectionName.DISK_FILE).find({
      creator: file.creator.id,
      deleted: enums.Bool.NO,
      _sort: 'updatedAt:DESC'
    });
    result.children = getAllChildren(list, file.id);
    result.children = result.children.filter(e => !e.is_folder);
    if (share) result.children = result.children.filter(e => e.shared);
    result.children = result.children.map( e => handleFileFields(e));
  }
  return ctx.wrapper.succ(result);
}

function handleFileFields(data) {
  data.creator = utils.pickFields(data.creator, ['id', 'username']);
  data.source_user = utils.pickFields(data.source_user, ['id', 'username']);
  delete data._id;
  delete data.type;
  delete data.source_id;
  delete data.pBranch;
  delete data.deleted;
  delete data.operator;
  return data;
}

const JOI_CREATE = Joi.object({
  period: Joi.string().required(),
  subject: Joi.string().required(),
  name: Joi.string().required(),
  size: Joi.number().optional(),
  type: Joi.string().optional().default(''),
  path: Joi.string().optional().default(''),
  url: Joi.string().optional().default(''),
  hash: Joi.string().optional().default(''),
  parent_id: Joi.string().optional().default('0'),
  is_folder: Joi.number().valid(0, 1).required(),
  need_parse: Joi.boolean().optional().default(false),
  task_type: Joi.string().default('word'),
  task_name: Joi.string(),
  grade: Joi.string().when('need_parse', { is: true, then: Joi.string().required() }),
  parse_type: Joi.string().when('need_parse', { is: true, then: Joi.string().required() }),
  from_year: Joi.number().optional(),
  to_year: Joi.number().when('need_parse', { is: true, then: Joi.number().required() })
});

async function addFile(ctx) {
  const {error, value} = JOI_CREATE.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const { name, period, subject, parent_id, path, hash, need_parse, task_type } = value;
  if (parent_id && parent_id !== ROOT_FOLDER) {
    const parent = await strapi.query(enums.CollectionName.DISK_FILE).findOne({id: parent_id, creator: user.id, deleted: enums.Bool.NO});
    if (_.isEmpty(parent)) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：目录不存在`);
  }
  // 根据路径判断是否需要创建目录
  const file_parent_id = await getUploadParentId(user, period, subject, parent_id, name, path);
  let doc = await getSameFile(user, file_parent_id, name, period, subject, enums.Bool.NO, hash);
  if (_.isEmpty(doc)) {
     doc = {
      period: period,
      subject: subject,
      name: value.name,
      size: value.size || 0,
      type: value.type || '',
      hash: value.hash || '',
      url: value.url || '',
      suffix: getExtName(name) || '',
      category: getFileCategory(name, value.type),
      parent_id: file_parent_id || '0',
      is_folder: value.is_folder,
      source: enums.FileSource.UPLOAD,
      source_id: enums.FileSource.UPLOAD,
      operator: user.id,
      creator: user.id,
      pBranch: user.pBranch.id,
      school_id: user.pBranch.yjSchoolId,
      user_id: user.yjUserId,
    };
    doc = await strapi.query(enums.CollectionName.DISK_FILE).create(doc);
  }
  await addLog(user, enums.FileOperationType.UPLOAD, doc);

  // 如果需要解析文件，调用解析服务
  if (need_parse && !doc.is_folder) {
    const { createAndStartTask } = require('../services/parse-task');
    const parseParams = {
      name: value.task_name,
      grade: value.grade,
      parse_type: value.parse_type,
      // from_year: value.from_year,
      to_year: value.to_year
    };
    if (!value.from_year) parseParams.from_year = parseParams.to_year - 1;

    try {
      await createAndStartTask(user, [doc], task_type, parseParams);
    } catch (error) {
      console.error('启动解析任务失败:', error);
      // 解析失败不影响文件上传成功
    }
  }
  await updateFolderTime(user, period, subject, parent_id);
  return ctx.wrapper.succ({id: doc.id});
}

const JOI_BATCH_CREATE = Joi.object({
  period: Joi.string().required(),
  subject: Joi.string().required(),
  files: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    size: Joi.number().required(),
    type: Joi.string().default(''),
    path: Joi.string().default('').allow(''),
    hash: Joi.string().required(),
    url: Joi.string().required(),
  })).required(),
  parent_id: Joi.string().optional().default('0'),
  need_parse: Joi.boolean().optional().default(false),
  task_type: Joi.string().valid('images', 'word').default('images'),
  task_name: Joi.string(),
  grade: Joi.string().when('need_parse', { is: true, then: Joi.string().required() }),
  parse_type: Joi.string().when('need_parse', { is: true, then: Joi.string().required() }),
  from_year: Joi.number().optional(),
  to_year: Joi.number().when('need_parse', { is: true, then: Joi.number().required() })
});

async function batchAddFile(ctx) {
  const {error, value } = JOI_BATCH_CREATE.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const { period, subject, parent_id, files, need_parse, task_type } = value;
  if (parent_id && parent_id !== ROOT_FOLDER) {
    const parent = await strapi.query(enums.CollectionName.DISK_FILE).findOne({id: parent_id, creator: user.id, deleted: enums.Bool.NO});
    if (_.isEmpty(parent)) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：目录不存在`);
  }
  // 根据路径判断是否需要创建目录
  let docs = []
  for (const file of files) {
    const file_parent_id = await getUploadParentId(user, period, subject, parent_id, file.name, file.path)
    let doc = await getSameFile(user, file_parent_id, file.name, period, subject, enums.Bool.NO, file.hash)
    if (_.isEmpty(doc)) {
       doc = {
        period: period,
        subject: subject,
        name: file.name,
        size: file.size || 0,
        type: file.type || '',
        hash: file.hash || '',
        url: file.url || '',
        suffix: getExtName(file.name) || '',
        category: getFileCategory(file.name, file.type),
        parent_id: file_parent_id || '0',
        is_folder: false,
        source: enums.FileSource.UPLOAD,
        source_id: enums.FileSource.UPLOAD,
        operator: user.id,
        creator: user.id,
        pBranch: user.pBranch.id,
        school_id: user.pBranch.yjSchoolId,
        user_id: user.yjUserId,
      }
      doc = await strapi.query(enums.CollectionName.DISK_FILE).create(doc)
    }
    await addLog(user, enums.FileOperationType.UPLOAD, doc)
    docs.push(doc)
  }
  // 如果需要解析文件，调用解析服务
  if (need_parse) {
    const { createAndStartTask } = require('../services/parse-task');
    const parseParams = {
      name: value.task_name,
      grade: value.grade,
      parse_type: value.parse_type,
      // from_year: value.from_year,
      to_year: value.to_year
    };
    if (!value.from_year) parseParams.from_year = parseParams.to_year - 1;

    try {
      await createAndStartTask(user, docs, task_type, parseParams);
    } catch (error) {
      console.error('启动解析任务失败:', error);
      // 解析失败不影响文件上传成功
    }
  }
  await updateFolderTime(user, period, subject, parent_id);
  return ctx.wrapper.succ({ids: docs.map(e => e.id)});
}


async function getUploadParentId(user, period, subject, parent_id, name, path) {
  let result = parent_id;
  if (!path) return result;
  const pathList = path.split('/');
  if (pathList.length === 1) return result;
  for (const p of pathList.slice(0, pathList.length -1)) {
    const doc = await strapi.query(enums.CollectionName.DISK_FILE).findOne({
      name: p,
      period,
      subject,
      parent_id: result,
      is_folder: enums.Bool.YES,
      creator: user.id
    });
    if (_.isEmpty(doc)) {
      const newDoc = await strapi.query(enums.CollectionName.DISK_FILE).create({
        name: p,
        period,
        subject,
        parent_id: result,
        is_folder: enums.Bool.YES,
        operator: user.id,
        creator: user.id,
        pBranch: user.pBranch.id,
        school_id: user.pBranch.yjSchoolId,
        user_id: user.yjUserId
      });
      result = newDoc.id;
    } else {
      result = doc.id;
    }
  }
  return result;
}


const JOI_UPDATE_FILE = Joi.object({
  id: Joi.string().required(),
  name: Joi.string().required(),
});

async function updateFile(ctx) {
  const {error, value} = JOI_UPDATE_FILE.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const {id, name} = value;
  const doc = await strapi.query(enums.CollectionName.DISK_FILE).findOne({id});
  if (_.isEmpty(doc) || doc.creator.id !== user.id || doc.deleted === enums.Bool.YES)
    return ctx.wrapper.error('HANDLE_ERROR', `资源不存在`);
  const upDoc = {
    name: name,
    operator: user.id
  };
  if (!doc.is_folder) {
    const suffix = getExtName(name) || '';
    if (doc.suffix !== suffix) upDoc.suffix = suffix;
    const category= getFileCategory(name) || '';
    if (doc.category !== category) upDoc.category = category;
  }
  await strapi.query(enums.CollectionName.DISK_FILE).update({id}, upDoc);
  await updateFolderTime(user, doc.period, doc.subject, doc.parent_id);
  return ctx.wrapper.succ('');
}

const JOI_IDS = Joi.array().items(Joi.string().length(24)).required().min(1);

async function deleteFile(ctx) {
  const {error, value} = JOI_IDS.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const query = {
    creator: user.id,
    deleted: enums.Bool.NO,
    _projection: {id: 1, parent_id: 1, is_folder: 1}
  };
  const list = await strapi.query(enums.CollectionName.DISK_FILE).find(query);
  const ids = [];
  for (const id of value) {
    const data  = list.find(e => e.id === id);
    if (_.isEmpty(data)) continue;
    if (data.is_folder === enums.Bool.YES) {
      // 获取所有的文件
      ids.push(id);
      const children = getAllChildren(list, id);
      if (_.size(children)) ids.push(...children.map(e => e.id));
    } else {
      ids.push(id);
    }
  }
  if (!_.size(ids)) return ctx.wrapper.succ({});
  const upDoc = {
    deleted: enums.Bool.YES
  };
  await strapi.query(enums.CollectionName.DISK_FILE).model.updateMany({_id: {$in: ids.map(e => new ObjectId(e))}}, {$set: upDoc});
  const item = list[0];
  await updateFolderTime(user, item.period, item.subject, item.parent_id);
  return ctx.wrapper.succ({});
}

function getAllChildren(items, parent_id) {
  const result = [];
  for (const item of items) {
    if (item.parent_id === parent_id) {
      const children = getAllChildren(items, item.id);
      if (children.length > 0) result.push(...children);
      result.push(item);
    }
  }
  return result;
}

const JOI_ID = Joi.object({
  id: Joi.string().length(24).required()
});

async function updateTopStatus(ctx) {
  const {error, value} = JOI_ID.validate(ctx.params);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const doc = await strapi.query(enums.CollectionName.DISK_FILE).findOne({id: value.id, operator: user.id, deleted: enums.Bool.NO});
  if (!_.isEmpty(doc)) {
    await strapi.query(enums.CollectionName.DISK_FILE).model.updateOne(
      {_id: new ObjectId(doc.id)},
      {$set: {is_top: doc.is_top ? enums.Bool.NO : enums.Bool.YES}},
      {timestamps: false});
    // await strapi.query(enums.CollectionName.DISK_FILE).update({id: doc.id}, {is_top: doc.is_top ? enums.Bool.NO : enums.Bool.YES});
  }
  return ctx.wrapper.succ({});
}

// 批量共享
async function batchShare(ctx) {
  const {error, value} = JOI_IDS.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const query = {
    creator: user.id,
    deleted: enums.Bool.NO,
    _projection: {id: 1, parent_id: 1, is_folder: 1, shared: 1}
  };
  const list = await strapi.query(enums.CollectionName.DISK_FILE).find(query);

  const ids = [];
  for (const id of value) {
    const data  = list.find(e => e.id === id);
    if (_.isEmpty(data)) continue;
    if (data.is_folder === enums.Bool.YES) {
      // 获取所有的文件
      const children = getAllChildren(list, id);
      if (_.size(children)) ids.push(..._.chain(children).filter(e => !e.is_folder).map(e => e.id).value());
    } else {
      ids.push(id);
    }
  }
  if (!_.size(ids)) return ctx.wrapper.succ({});
  const upDoc = {
    shared: enums.Bool.YES
  };
  await strapi.query(enums.CollectionName.DISK_FILE).model.updateMany({_id: {$in: ids.map(e => new ObjectId(e))}}, {$set: upDoc}, {timestamps: false});
  return ctx.wrapper.succ({});
}

// 取消共享
async function cancelShare(ctx) {
  const {error, value} = JOI_IDS.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const query = {
    creator: user.id,
    deleted: enums.Bool.NO,
    _projection: {id: 1, parent_id: 1, is_folder: 1, shared: 1}
  };
  const list = await strapi.query(enums.CollectionName.DISK_FILE).find(query);
  const ids = [];
  for (const id of value) {
    const data  = list.find(e => e.id === id);
    if (_.isEmpty(data)) continue;
    if (data.is_folder === enums.Bool.YES) {
      // 获取所有的文件
      let children = getAllChildren(list, id).filter(e => !e.is_folder && e.shared);
      if (_.size(children)) ids.push(...children.map(e => e.id));
    } else {
      if (data.shared) ids.push(id);
    }
  }
  if (!_.size(ids)) return ctx.wrapper.succ({});
  const upDoc = {
    shared: enums.Bool.NO
  };
  await strapi.query(enums.CollectionName.DISK_FILE).model.updateMany({_id: {$in: ids.map(e => new ObjectId(e))}}, {$set: upDoc}, {timestamps: false});
  return ctx.wrapper.succ({});
}


const JOI_UPDATE_FOLDER = Joi.object({
  period: Joi.string().required(),
  subject: Joi.string().required(),
  ids: Joi.array().items(Joi.string().length(24)).min(1),
  folder: Joi.string().required()
});

async function updateFolder(ctx) {
  const {error, value} = JOI_UPDATE_FOLDER.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const { period, subject, folder, ids } = value;
  const user = ctx.state.user;
  if (folder !== ROOT_FOLDER) {
    const targetFolder = await strapi.query(enums.CollectionName.DISK_FILE).findOne({ id: folder});
    if (_.isEmpty(targetFolder) || !targetFolder.is_folder) return ctx.wrapper.error('HANDLE_ERROR', `不可移动到该位置`);
  }
  // 检查是自身或者子级目录
  const allFolder = await strapi.query(enums.CollectionName.DISK_FILE).find({
    period,
    subject,
    is_folder: enums.Bool.YES,
    creator: user.id,
    deleted: enums.Bool.NO
  });
  const list = await strapi.query(enums.CollectionName.DISK_FILE).find({id_in: ids, creator: user.id});
  let isSelfOrChild = false;
  for (const data of list) {
    if (data.parent_id === folder) { // 没有移动
      isSelfOrChild = true;
      break;
    }
    if (!data.is_folder) continue;
    if (data.id === folder) {
      isSelfOrChild = true;
      break;
    }
    const children = getAllChildren(allFolder, data.id);
    if (_.size(children)) {
      isSelfOrChild = true;
      break;
    }
  }
  if (isSelfOrChild) return ctx.wrapper.error('HANDLE_ERROR', `不可移动自身或者及其子目录下`);
  const upDoc = {
    parent_id: folder
  }
  await strapi.query(enums.CollectionName.DISK_FILE).model.updateMany({_id: {$in: ids.map(e => new ObjectId(e))}}, {$set: upDoc});
  await updateFolderTime(user, period, subject, folder);
  return ctx.wrapper.succ({});
}

// 下载
async function download(ctx) {
  const {error, value} = JOI_ID.validate(ctx.params);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const {id} = value;
  const data = await strapi.query(enums.CollectionName.DISK_FILE).findOne({id});
  if (_.isEmpty(data) || data.is_folder || data.pBranch.id !== user.pBranch.id || data.deleted)
    return ctx.wrapper.error('HANDLE_ERROR', `资源不存在`);
  // 可以下载自己的或者别人共享的
  if (data.creator.id !== user.id && data.shared === enums.Bool.NO)
    return ctx.wrapper.error('HANDLE_ERROR', `不可下载`);
  await addLog(user, enums.FileOperationType.DOWNLOAD, data);
  return ctx.wrapper.succ(data);
}

const JOI_GET_SHARE_USERS = Joi.object({
  period: Joi.string().required(),
  subject: Joi.string().required(),
});
// 获取所有的分享用户信息
async function getShareUsers(ctx) {
  const {error, value} = JOI_GET_SHARE_USERS.validate(ctx.request.query);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const { period, subject } = value;
  const user = ctx.state.user;
  const list = await strapi.query(enums.CollectionName.DISK_FILE).model.distinct('creator', {
    period, subject,
    pBranch: user.pBranch.id,
    shared: enums.Bool.YES,
    deleted: enums.Bool.NO
  });
  let users = await strapi.query('user', 'users-permissions').find({id_in: list.map(e => e.toString()), _projection: {id: 1, username: 1}});
  if (_.size(users)) {
    users = users.map(e => {
      return _.pick(e, ['id', 'username']);
    });
    const list = await strapi.query(enums.CollectionName.DISK_FILE).model.aggregate([
      {
        $match: {
          deleted: 0,
          creator: { $in: users.map(e => new ObjectId(e.id.toString())) }
        }
      },
      {
        $sort: {
          updatedAt: -1 // 假设有一个创建时间字段，按时间降序排序
        }
      },
      {
        $group: {
          _id: "$creator",
          latestRecord: { $first: "$$ROOT" }
        }
      },
      {
        $replaceRoot: { newRoot: "$latestRecord" }
      }
    ]);
    for (const user of users) {
      const data = list.find(e => e.creator.toString() === user.id.toString());
      if (!_.isEmpty(data)) {
        user.updatedAt = data.updatedAt;
      } else {
        user.updatedAt = new Date();
      }
    }
    users = _.orderBy(users, ['updatedAt'], ['desc']);
  }
  const result = {
    self: users.find(e => e.id === user.id) || null,
    users: users.filter(e => e.id !== user.id) || []
  }
  if (result.self) result.self.username = '我的共享';
  return ctx.wrapper.succ(result);
}

const JOI_SEARCH_SHARE_USER_FILES = Joi.object({
  offset: Joi.number().required(),
  limit: Joi.number().min(1).required(),
  period: Joi.string().required(),
  subject: Joi.string().required(),
  share_user: Joi.string().required(),
  parent_id: Joi.string().required(),
  name: Joi.string().optional().allow(''),
  suffixes: Joi.array().items(Joi.string()).optional(),
  // category: Joi.string().optional().allow(''),
  category: Joi.array().items(Joi.string()).optional(),
});

async function searchShareUserFiles(ctx) {
  const {error, value} = JOI_SEARCH_SHARE_USER_FILES.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const { offset, limit, period, subject, share_user, parent_id, name, suffixes, category } = value;
  const user = ctx.state.user;
  let allList = await strapi.query(enums.CollectionName.DISK_FILE).find({
    period,
    subject,
    creator: share_user,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO,
    _sort: 'updatedAt:DESC'
  });
  const result = {
    total: 0,
    list: []
  }
  if (!_.size(allList)) return ctx.wrapper.succ(result);
  const list = allList.filter(e => {
    if (e.parent_id !== parent_id) return false
    if (name && !e.name.includes(name)) return false;
    if (_.size(category) && !category.includes(e.category)) return false;
    if (_.size(suffixes) && !suffixes.includes(e.suffix)) return false;
    if (e.is_folder) {
      const children = getAllChildren(allList, e.id).filter(e => e.shared && !e.is_folder);
      if (!_.size(children)) return false;
      return true;
    } else {
      return !!e.shared;
    }
  });
  result.total = _.size(list);
  if (!result.total) return ctx.wrapper.succ(result);
  //
  const pages = _.chunk(list, limit);
  result.list = pages[Math.floor(offset / limit)];
  for (const data of result.list) {
    handleFileFields(data);
  }
  return ctx.wrapper.succ(result);
}

const JOI_REF_FILES = Joi.object({
  period: Joi.string().required(),
  subject: Joi.string().required(),
  share_user: Joi.string().required(),
  folder: Joi.string().required(),
  ids: Joi.array().items(Joi.string().length(24)).min(1)
});

// 转存
async function ref(ctx) {
  const {error, value} = JOI_REF_FILES.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const {period, subject, share_user, folder, ids} = value;
  if (user.id === share_user) return ctx.wrapper.error('HANDLE_ERROR', `不能转存自己`);
  if (folder && folder !== ROOT_FOLDER) {
    const parent = await strapi.query(enums.CollectionName.DISK_FILE).findOne({id: folder, creator: user.id, deleted: enums.Bool.NO});
    if (_.isEmpty(parent)) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：目录不存在`);
  }
  const shareList = await strapi.query(enums.CollectionName.DISK_FILE).find({
    period,
    subject,
    creator: share_user,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO
  });
  if (!_.size(shareList)) return ctx.wrapper.succ({});
  //
  for (const source_id of ids) {
    const source_data = shareList.find(e => e.id === source_id);
    if (source_data.is_folder) { // 目录-递归处理所有子目录及文件
      await copyFolder(user, period, subject, folder, shareList, source_data);
    } else { // 文件
      await copyFile(user, folder, source_data);
    }
  }
  await updateFolderTime(user, period, subject, folder);
  return ctx.wrapper.succ({});
}

async function updateFolderTime(user, period, subject, folder_id) {
  if (folder_id === ROOT_FOLDER) return; //
  const list = await strapi.query(enums.CollectionName.DISK_FILE).find({
    period,
    subject,
    creator: user.id,
    is_folder: enums.Bool.YES,
    deleted: enums.Bool.NO,
    _projection: {id: 1, parent_id: 1}
  });
  function findAllParents(id, result = []) {
    const item = list.find(i => i.id === id);
    if (!item) return result;
    result.unshift(item); // 添加到数组开头以保持顺序
    if (item.parent_id !== ROOT_FOLDER) {
      findAllParents(item.parent_id, result);
    }
    return result;
  }
  const items = findAllParents(folder_id);
  if (_.size(items)) {
    const ids = items.map(e => e.id);
    await strapi.query(enums.CollectionName.DISK_FILE).model.updateMany({_id: {$in: ids.map(e => new ObjectId(e))}}, {$set: {deleted: enums.Bool.NO}});
  }
}

function findAllParents(id, result = []) {
  const item = list.find(i => i.id === id);
  if (!item) return result;

  result.unshift(item); // 添加到数组开头以保持顺序

  if (item.parent_id !== 0) {
    findAllParentsRecursive(item.parent_id, result);
  }

  return result;
}

async function copyFolder(user, period, subject, target_folder, source_list, source_data) {
  // let folderData = await strapi.query(enums.CollectionName.DISK_FILE).findOne({
  //   creator: user.id,
  //   period,
  //   subject,
  //   name: source_data.name,
  //   parent_id: target_folder,
  //   is_folder: enums.Bool.YES,
  //   deleted: enums.Bool.NO
  // });
  // if (_.isEmpty(folderData)) {
  //   folderData = await copyFile(user, target_folder, source_data);
  // }
  const folderData = await copyFile(user, target_folder, source_data);
  const children = source_list.filter(e => e.parent_id === source_data.id);
  if (!_.size(children)) return;
  for (const data of children) {
    if (data.is_folder) {
      await copyFolder(user, period, subject, folderData.id, source_list, data);
    } else {
      await copyFile(user, folderData.id, data);
    }
  }
}

async function copyFile(user, target_folder, source_data) {
  if (!source_data.is_folder && !source_data.shared) return;
  // 查询目录下是否有相同数据
  const sameFile = await getSameFile(user, target_folder, source_data.name, source_data.period, source_data.subject, source_data.is_folder, source_data.hash);
  // if (!_.isEmpty(sameFile)) return sameFile;
  const newData = _.pick(source_data, ['name', 'period', 'subject', 'size', 'type', 'suffix', 'category', 'hash', 'url', 'is_folder']);
  if (_.size(sameFile)) {
    if (sameFile.is_folder) {
      newData.name = newData.name + `（1）`;
    } else {
      const arr = newData.name.split('.');
      newData.name = `${arr[0]}（1）.${arr[1]}`;
    }
  }
  newData.parent_id = target_folder;
  newData.source = enums.FileSource.REF;
  newData.source_id = source_data.id;
  newData.source_user = source_data.creator.id;
  newData.operator = user.id;
  newData.creator = user.id;
  newData.pBranch = user.pBranch.id;
  newData.school_id = user.pBranch.yjSchoolId;
  newData.user_id = user.yjUserId;
  return await strapi.query(enums.CollectionName.DISK_FILE).create(newData);
}

async function getSameFile(user, parent_id, name, period, subject, is_folder, hash) {
  const query = {
    creator: user.id,
    name: name,
    period: period,
    subject: subject,
    parent_id: parent_id,
    is_folder: is_folder,
    hash: hash,
    deleted: enums.Bool.NO
  };
  return await strapi.query(enums.CollectionName.DISK_FILE).findOne(query);
}

async function addLog(user, operation_type, data) {
  if (data.is_folder) return;
  const doc = {
    name: data.name,
    period: data.period,
    subject: data.subject,
    file: data.id,
    operation_type,
    creator: user.id,
    school_id: user.pBranch.yjSchoolId,
    user_id: user.yjUserId
  }
  await strapi.query(enums.CollectionName.DISK_FILE_LOG).create(doc);
}


// 文件分类映射
const fileCategories = {
  document: {
    extensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
    mimeTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain'
    ]
  },
  image: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
    mimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp',
      'image/svg+xml'
    ]
  },
  video: {
    extensions: ['.mp4', '.webm', '.avi', '.mov', '.mkv', '.flv'],
    mimeTypes: [
      'video/mp4',
      'video/webm',
      'video/x-msvideo',
      'video/quicktime',
      'video/x-matroska',
      'video/x-flv'
    ]
  },
  audio: {
    extensions: ['.mp3', '.wav', '.ogg', '.m4a', '.flac'],
    mimeTypes: [
      'audio/mpeg',
      'audio/wav',
      'audio/ogg',
      'audio/x-m4a',
      'audio/flac'
    ]
  }
};

function getExtName (name) {
  return path.extname(name).toLowerCase();
}
// 获取文件分类
function getFileCategory(file, mimeType) {
  let ext = getExtName(file);
  if (!ext) return '';
  // const mimeType = mime.lookup(ext) || file.mimetype;
  // 检查每种分类
  for (const [category, config] of Object.entries(fileCategories)) {
    if (config.extensions.includes(ext) || config.mimeTypes.includes(mimeType)) {
      return category;
    }
  }
  return enums.FileCategory.OTHER;
}



