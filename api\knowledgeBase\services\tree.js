const _ = require('lodash')
const { ObjectId } = require('mongodb')

module.exports = {
  replaceChapterField,
  deleteLastLevelByIds,
  getTreeBulkWriteArray,
  addKnowledges,
}

//  递归替换树中章节对象的字段
function replaceChapterField (obj, chapterMap, needKnowledge = true) {
  // 如果当前对象是数组，则遍历数组中的每个元素
  if (Array.isArray(obj)) {
    for (const key in obj) {
      if (!chapterMap[obj[key].id.toString()]) {
        _.pullAt(obj, key)
        continue
      }
      replaceChapterField(obj[key], chapterMap, needKnowledge)
    }
  }
  // 如果当前对象是对象类型，则更新该对象字段
  else if (typeof obj === 'object' && obj !== null) {
    const curChapter = chapterMap[obj.id.toString()]
    obj.name = curChapter.name
    obj.source = curChapter.source
    obj.source_id = curChapter.source_id
    curChapter.match = true

    // 如果对象中知识点，视为章节根节点，且处理知识点
    if (curChapter.knowledges && curChapter.knowledges.length > 0) {
      // 根据 needKnowledge 参数决定是否处理知识点信息
      obj.knowledges = curChapter.knowledges
      if (needKnowledge) {
        obj.children = curChapter.knowledges.map(knowledge => {
          return {
            id: knowledge.id,
            name: knowledge.name,
            source: knowledge.source,
            source_id: knowledge.source_id,
            key: 'knowledge',
          }
        })
      } else {
        delete obj.children
      }
    } else if (Array.isArray(obj.children) && obj.children.length > 0) {
      // 如果当前对象的 'children' 字段为数组且不为空，则递归处理
      replaceChapterField(obj.children, chapterMap, needKnowledge)
    }
  }
}

function deleteLastLevelByIds (data, idArray) {
  // 递归函数，用于查找并删除目标节点
  function recursiveDelete (node, ids, index) {
    if (!node || !Array.isArray(node.children)) {
      return
    }
    // 遍历当前节点的子节点
    for (let i = 0; i < node.children.length; i++) {
      const child = node.children[i]

      // 如果当前子节点的 id 匹配 idArray 中的当前层级 id
      if (child.id === ids[index]) {
        // 如果已经匹配到最后一个 id，删除该节点
        if (index === ids.length - 1) {
          node.children.splice(i, 1) // 删除当前节点
          return
        } else {
          // 否则继续递归查找下一层级
          recursiveDelete(child, ids, index + 1)
        }
      }
    }
  }

  // 从根节点开始递归
  recursiveDelete(data, idArray, 0)
}

// 根据树结构 批量更新或插入章节、知识点 
async function getTreeBulkWriteArray (info, path, updateData, knowledgeMap, treeModel, chapterModel) {
  let chapterBulkWriteArray = [], chapterInsertArray = [], knowledgeBulkWriteArray = [], knowledgeInsertArray = []
  let knowledgeInsertMap = {}

  function getNewChildren (obj, path) {
    // 如果当前对象是数组，则遍历数组中的每个元素
    if (Array.isArray(obj)) {
      obj.forEach(item => getNewChildren(item, path))
    }
    // 如果当前对象是对象类型，
    else if (typeof obj === 'object' && obj !== null) {
      // 如果对象没有ID，说明需要插入新的记录
      const isNewChapter = !obj.id
      let isEnd = false
      if (!obj.id) {
        obj.id = new ObjectId().toString()
      }
      let knowledgeIds = []
      // 如果对象有子元素且是知识点，说明需要单独处理知识点
      if (Array.isArray(obj.children) && obj.children.length > 0 && obj.children[0].key === 'knowledge') {
        // 不递归知识点，标记带有知识点的章节节点为根节点
        isEnd = true
        for (let knowledge of obj.children) {
          // 如果知识点没有ID，说明需要插入新的记录
          if (!knowledge.id) {
            // 如果存在同名知识点，则使用已有知识点的ID
            const curKnowledge = knowledgeMap[knowledge.name] || knowledgeInsertMap[knowledge.name]
            if (curKnowledge) {
              knowledge.id = curKnowledge.id || curKnowledge._id
              // 如果是同步知识点，则更新知识点管理kb的source_id
              if (knowledge.source_id && !curKnowledge.source_id) {
                knowledgeBulkWriteArray.push({
                  updateOne: {
                    filter: {
                      _id: knowledge.id
                    },
                    update: {
                      $set: {
                        source_id: knowledge.source_id,
                        operator: updateData.operator,
                      }
                    }
                  }
                })
              }
            } else {
              knowledge.id = new ObjectId().toString()
              let knowledgeUpdateData = {
                ...updateData,
                _id: knowledge.id,
                name: knowledge.name,
                source: knowledge.source,
                source_id: knowledge.source_id,
                key: `${knowledge.name}-${updateData.period}-${updateData.subject}-${updateData.pBranch}`,
              }
              delete knowledgeUpdateData.press_version
              delete knowledgeUpdateData.book
              delete knowledgeUpdateData.knowledge_tree
              knowledgeInsertMap[knowledge.name] = knowledgeUpdateData
              knowledgeInsertArray.push(knowledgeUpdateData)
            }
          }
          knowledgeIds.push(knowledge.id)
        }
        delete obj.children
      }

      // 插入或更新章节节点
      if (isNewChapter) {
        chapterInsertArray.push({
          ...updateData,
          _id: obj.id,
          name: obj.name,
          source: obj.source,
          source_id: obj.source_id,
          knowledges: knowledgeIds,
          path: `${path}-${obj.id}`,
          key: `${path}-${obj.name}`,
        })
      } else if (knowledgeIds.length > 0) {
        chapterBulkWriteArray.push({
          updateOne: {
            filter: { _id: obj.id },
            update: {
              $set: {
                knowledges: knowledgeIds,
                operator: updateData.operator,
              }
            },
          }
        })
      }
      // 章节根节点 则不在递归
      if (isEnd) return

      let curPath = `${path}-${obj.id}`
      // 如果对象中有 'children' 字段，递归处理
      if ((Array.isArray(obj.children) && obj.children.length > 0)) {
        getNewChildren(obj.children, curPath)
      }
    }
  }

  getNewChildren(info.children, path)
  // 更新教材 章节次序
  await strapi.query(treeModel).update({ id: info.id }, { children: info.children, operator: updateData.operator })
  // 批量插入或更新 章节知识点
  if (knowledgeInsertArray.length > 0) {
    let result = await strapi.query('knowledge').model.insertMany(knowledgeInsertArray)
    // console.log(result)
  }
  if (knowledgeBulkWriteArray.length > 0) {
    let result = await strapi.query('knowledge').model.bulkWrite(knowledgeBulkWriteArray)
    // console.log(result)
  }
  if (chapterInsertArray.length > 0) {
    let result = await strapi.query(chapterModel).model.insertMany(chapterInsertArray)
    // console.log(result)
  }
  if (chapterBulkWriteArray.length > 0) {
    let result = await strapi.query(chapterModel).model.bulkWrite(chapterBulkWriteArray)
    // console.log(result)
  }
}

// 章节添加知识点
async function addKnowledges (id, userId, knowledges, chapterModel) {
  let chapter = await strapi.query(chapterModel).findOne({ id: id }, [])
  let knowledgeInsertArray = [], knowledgeIds = []
  for (let knowledge of knowledges) {
    if (!knowledge.id) {
      const key = `${knowledge.name}-${chapter.period}-${chapter.subject}-${chapter.pBranch}`
      let curKnowledge = await strapi.query('knowledge').findOne({ key: key }, [])
      if (!curKnowledge) {
        knowledge.id = new ObjectId().toString()
        knowledgeInsertArray.push({
          _id: knowledge.id,
          name: knowledge.name,
          source: 'manual',
          key: key,
          period: chapter.period,
          subject: chapter.subject,
          operator: userId,
          creator: userId,
          pBranch: chapter.pBranch,
        })
      } else {
        knowledge.id = curKnowledge.id
      }
    }
    knowledgeIds.push(knowledge.id)
  }
  // 如果有新的知识条目需要插入，执行批量插入操作
  if (knowledgeInsertArray.length > 0) {
    let result = await strapi.query('knowledge').model.insertMany(knowledgeInsertArray)
  }
  // 更新章节，添加新的知识条目ID，更新章节知识点顺序
  return await strapi.query(chapterModel).update({ id: id }, { knowledges: knowledgeIds, operator: userId, })
}