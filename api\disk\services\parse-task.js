'use strict'
const axios = require('axios')

const enums = require('../../common/enums')
const client = require('../../common/client')
const server = strapi.config.server.openTiku
const archiver = require('archiver')
const _ = require('lodash')

// 创建并启动解析任务
async function createAndStartTask (user, files, task_type, params) {
  if (!user.unifyToken) {
    throw new Error('未识别的阅卷用户')
  }

  // 创建解析任务
  const task = {
    name: params.name || files[0].name,
    period: files[0].period,
    subject: files[0].subject,
    // disk_file: file.id,
    disk_files: files.map(file => file.id),
    task_type: task_type,
    grade: params.grade,
    type: params.parse_type,
    from_year: params.from_year,
    to_year: params.to_year,
    status: 'init', // 初始状态为划题中
    school_id: user.pBranch?.yjSchoolId,
    user_id: user.yjUserId,
    creator: user.id,
    pBranch: user.pBranch.id
  }

  const doc = await strapi.query(enums.CollectionName.PARSE_TASK).create(task)
  // await strapi.query(enums.CollectionName.DISK_FILE).update(
  //   { id: file.id },
  //   { parse_task: doc.id }
  // )
  // 开始解析文件
  startParseTask(user, doc, files)

  return doc
}

async function startParseTask (user, task, files) {
  try {
    // 调用解析服务
    let result
    const params = {
      username: user.username,
      name: task.name,
      period: task.period,
      subject: task.subject,
      grade: task.grade,
      type: task.type,
    }
    if (task.task_type === 'word') {
      result = await client.file.wordParse(Object.assign(params, { word_url: files[0].url }))
    } else if (task.task_type === 'images') {
      result = await client.file.imagesParse(Object.assign(params, { files: files.map(file => file.url) }))
    } else if (task.task_type === 'pdf') {
      result = await client.file.wordParse(Object.assign(params, { pdf_url: files[0].url }))
    } else {
      throw new Error('解析任务类型错误')
    }

    // // 取消解析失败状态
    // if (result.code !== 0) {
    //   // 解析失败
    //   await strapi.query(enums.CollectionName.PARSE_TASK).update(
    //     { id: task.id },
    //     { status: 'error', error: result.msg || '解析失败' }
    //   )
    //   return
    // }

    // 解析成功，更新状态为待复核
    await strapi.query(enums.CollectionName.PARSE_TASK).update(
      { id: task.id },
      { status: 'edit' }
    )

    // 保存解析结果等
    let tikuResult = await axios.post(server.url + '/v1/user_paper/upload/parse/callback', {
      task_id: task.id,
      data: result.data || {},
    }, {
      headers: {
        'Cookie': `unify_sid=${user.unifyToken}`
      }
    })
    if (!tikuResult.data || tikuResult.data.code !== 0 || !tikuResult.data.data) {
      // 解析失败
      // await strapi.query(enums.CollectionName.PARSE_TASK).update(
      //   { id: task.id },
      //   { status: 'error', error: '试卷创建失败' }
      // )
      // return
      // 解析结果 异常，则创建空试卷
      tikuResult = await axios.post(server.url + '/v1/user_paper/upload/parse/callback', {
        task_id: task.id,
        data: {},
      }, {
        headers: {
          'Cookie': `unify_sid=${user.unifyToken}`
        }
      })
    }

    await strapi.query(enums.CollectionName.PARSE_TASK).update(
      { id: task.id },
      { status: tikuResult.data?.data?.status, error: tikuResult.data?.data?.error, tiku_paper_id: tikuResult.data?.data?.paper_id }
    )

  } catch (error) {
    console.error('解析任务执行失败:', error)
    await strapi.query(enums.CollectionName.PARSE_TASK).update(
      { id: task.id },
      { status: 'error', error: error.message || '解析过程异常' }
    )
  }
}

/**
 * 验证解析任务权限和有效性
 * @param {string} taskId - 任务ID
 * @param {object} user - 用户对象
 * @returns {Promise<object>} 任务对象
 */
async function validateTaskAccess (taskId, user) {
  const task = await strapi.query(enums.CollectionName.PARSE_TASK).findOne({
    id: taskId,
    creator: user.id,
    pBranch: user.pBranch.id,
    deleted: enums.Bool.NO
  })

  if (!task) {
    throw new Error('解析任务不存在或无权限访问')
  }

  // 检查任务类型是否为images
  if (task.task_type !== 'images') {
    throw new Error('只有图片解析任务才支持批量下载ZIP')
  }

  return task
}

/**
 * 获取任务关联的有效文件列表
 * @param {object} task - 任务对象
 * @returns {Promise<Array>} 有效文件列表
 */
async function getTaskFiles (task) {
  // 获取关联的磁盘文件
  const diskFiles = await strapi.query(enums.CollectionName.DISK_FILE).find({
    id_in: task.disk_files || [],
    deleted: enums.Bool.NO
  })

  if (_.isEmpty(diskFiles)) {
    throw new Error('未找到相关的文件')
  }

  // 过滤出有效的URL
  const validFiles = diskFiles.filter(file => file.url && file.url.trim())

  if (validFiles.length === 0) {
    throw new Error('没有有效的文件URL')
  }

  if (validFiles.length > 10) {
    throw new Error('文件数量过多，一次最多支持10个文件')
  }

  return validFiles
}

module.exports = {
  createAndStartTask,
  validateTaskAccess,
  getTaskFiles
}
