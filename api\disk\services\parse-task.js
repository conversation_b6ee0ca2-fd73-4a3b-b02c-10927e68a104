'use strict'
const axios = require('axios')

const enums = require('../../common/enums')
const client = require('../../common/client')
const server = strapi.config.server.openTiku

// 创建并启动解析任务
async function createAndStartTask (user, files, task_type, params) {
  if (!user.unifyToken) {
    throw new Error('未识别的阅卷用户')
  }

  // 创建解析任务
  const task = {
    name: params.name || files[0].name,
    period: files[0].period,
    subject: files[0].subject,
    // disk_file: file.id,
    disk_files: files.map(file => file.id),
    task_type: task_type,
    grade: params.grade,
    type: params.parse_type,
    from_year: params.from_year,
    to_year: params.to_year,
    status: 'init', // 初始状态为划题中
    school_id: user.pBranch?.yjSchoolId,
    user_id: user.yjUserId,
    creator: user.id,
    pBranch: user.pBranch.id
  }

  const doc = await strapi.query(enums.CollectionName.PARSE_TASK).create(task)
  // await strapi.query(enums.CollectionName.DISK_FILE).update(
  //   { id: file.id },
  //   { parse_task: doc.id }
  // )
  // 开始解析文件
  startParseTask(user, doc, files)

  return doc
}

async function startParseTask (user, task, files) {
  try {
    // 调用解析服务
    let result
    const params = {
      username: user.username,
      name: task.name,
      period: task.period,
      subject: task.subject,
      grade: task.grade,
      type: task.type,
    }
    if (task.task_type === 'word') {
      result = await client.file.wordParse(Object.assign(params, { word_url: files[0].url }))
    } else if (task.task_type === 'images') {
      result = await client.file.imagesParse(Object.assign(params, { files: files.map(file => file.url) }))
    } else {
      throw new Error('解析任务类型错误')
    }

    if (result.code !== 0) {
      // 解析失败
      await strapi.query(enums.CollectionName.PARSE_TASK).update(
        { id: task.id },
        { status: 'error', error: result.msg || '解析失败' }
      )
      return
    }

    // 解析成功，更新状态为待复核
    await strapi.query(enums.CollectionName.PARSE_TASK).update(
      { id: task.id },
      { status: 'edit', ai_task_id: result.data?.task_id || null }
    )

    // 保存解析结果等
    const tikuResult = await axios.post(server.url + '/v1/user_paper/upload/parse/callback', {
      task_id: task.id,
      data: result.data
    }, {
      headers: {
        'Cookie': `unify_sid=${user.unifyToken}`
      }
    })
    if (!tikuResult.data || tikuResult.data.code !== 0 || !tikuResult.data.data) {
      // 解析失败
      await strapi.query(enums.CollectionName.PARSE_TASK).update(
        { id: task.id },
        { status: 'error', error: '试卷创建失败' }
      )
      return
    }

    await strapi.query(enums.CollectionName.PARSE_TASK).update(
      { id: task.id },
      { status: tikuResult.data?.data?.status, error: tikuResult.data?.data?.error, tiku_paper_id: tikuResult.data?.data?.paper_id }
    )

  } catch (error) {
    console.error('解析任务执行失败:', error)
    await strapi.query(enums.CollectionName.PARSE_TASK).update(
      { id: task.id },
      { status: 'error', error: error.message || '解析过程异常' }
    )
  }
}

module.exports = {
  createAndStartTask
}
