module.exports = {
  collectionName: 'knowledge-tree',
  info: {
    name: 'knowledge-tree',
    label: '知识树',
    description: '知识树'
  },
  options: {
    timestamps: true,
    indexes: [
      { keys: { key: 1 }, options: { unique: true } },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '知识点名称',
      type: 'string',
      required: true,
    },
    source: {
      label: '来源',
      type: 'string',
      default: 'manual',
      options: [
        {
          label: '同步',
          value: 'sync'
        },
        {
          label: '手动',
          value: 'manual'
        }
      ]
    },
    source_id: {
      label: '来源id',
      type: 'number',
    },
    knowledge_tree_chapter:{
      label: '知识树章节',
      model: 'knowledge-tree-chapter',
      required: true,
    },
    key: {
      label: 'key', // name-subject-pBranch
      required: true,
      type: 'string',
    },
    children: {
      label: '教材章节',
      type: 'json'
    },
    // children: [{
    //   id: int,
    //   name: string,       //一级知识点名称
    //   children: [{
    //     id: int,        //对应knowledge_tree_chapter中的_id
    //     name: string,   //X级知识点名称
    //   }]
    // }],
    period: {
      label: '学段',
      type: 'string',
      // model: 'period',
      required: true,
    },
    subject: {
      label: '学科',
      type: 'string',
      // model: 'subject',
      required: true,
    },
    operator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    creator: {
      label: '操作人',
      plugin: 'users-permissions',
      model: 'user',
      visible: false,
      configurable: false,
    },
    pBranch: {
      label: '租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    },
  }
}
