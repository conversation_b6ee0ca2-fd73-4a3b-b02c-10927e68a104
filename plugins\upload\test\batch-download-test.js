/**
 * 批量下载ZIP功能测试脚本
 */

const axios = require('axios')
const fs = require('fs')
const path = require('path')

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:1337',
  endpoint: '/upload/external/batch-download-zip',
  outputDir: './test-downloads'
}

// 确保输出目录存在
if (!fs.existsSync(TEST_CONFIG.outputDir)) {
  fs.mkdirSync(TEST_CONFIG.outputDir, { recursive: true })
}

/**
 * 测试1: 基本功能测试
 */
async function testBasicFunctionality() {
  console.log('\n=== 测试1: 基本功能测试 ===')
  
  const testUrls = [
    'https://httpbin.org/json',
    'https://httpbin.org/xml',
    'https://httpbin.org/html'
  ]
  
  try {
    const response = await axios.post(TEST_CONFIG.baseUrl + TEST_CONFIG.endpoint, {
      urls: testUrls,
      zipName: 'basic-test.zip',
      timeout: 10000
    }, {
      responseType: 'stream',
      timeout: 30000
    })
    
    const outputPath = path.join(TEST_CONFIG.outputDir, 'basic-test.zip')
    const writer = fs.createWriteStream(outputPath)
    response.data.pipe(writer)
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log('✅ 基本功能测试通过')
        console.log(`   文件保存至: ${outputPath}`)
        resolve()
      })
      
      writer.on('error', (err) => {
        console.log('❌ 基本功能测试失败:', err.message)
        reject(err)
      })
    })
  } catch (error) {
    console.log('❌ 基本功能测试失败:', error.message)
    throw error
  }
}

/**
 * 测试2: 自定义文件名测试
 */
async function testCustomFilenames() {
  console.log('\n=== 测试2: 自定义文件名测试 ===')
  
  const testUrls = [
    {
      url: 'https://httpbin.org/json',
      filename: '自定义JSON文件.json'
    },
    {
      url: 'https://httpbin.org/xml',
      filename: 'custom-xml-file.xml'
    },
    'https://httpbin.org/html' // 混合格式
  ]
  
  try {
    const response = await axios.post(TEST_CONFIG.baseUrl + TEST_CONFIG.endpoint, {
      urls: testUrls,
      zipName: '自定义文件名测试.zip',
      timeout: 10000
    }, {
      responseType: 'stream',
      timeout: 30000
    })
    
    const outputPath = path.join(TEST_CONFIG.outputDir, 'custom-filenames-test.zip')
    const writer = fs.createWriteStream(outputPath)
    response.data.pipe(writer)
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log('✅ 自定义文件名测试通过')
        console.log(`   文件保存至: ${outputPath}`)
        resolve()
      })
      
      writer.on('error', (err) => {
        console.log('❌ 自定义文件名测试失败:', err.message)
        reject(err)
      })
    })
  } catch (error) {
    console.log('❌ 自定义文件名测试失败:', error.message)
    throw error
  }
}

/**
 * 测试3: 错误处理测试
 */
async function testErrorHandling() {
  console.log('\n=== 测试3: 错误处理测试 ===')
  
  const testUrls = [
    'https://httpbin.org/json', // 有效URL
    'https://httpbin.org/status/404', // 404错误
    'https://invalid-domain-that-does-not-exist.com/file.txt', // 无效域名
    'https://httpbin.org/xml' // 有效URL
  ]
  
  try {
    const response = await axios.post(TEST_CONFIG.baseUrl + TEST_CONFIG.endpoint, {
      urls: testUrls,
      zipName: 'error-handling-test.zip',
      timeout: 5000
    }, {
      responseType: 'stream',
      timeout: 30000
    })
    
    const outputPath = path.join(TEST_CONFIG.outputDir, 'error-handling-test.zip')
    const writer = fs.createWriteStream(outputPath)
    response.data.pipe(writer)
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log('✅ 错误处理测试通过')
        console.log(`   文件保存至: ${outputPath}`)
        console.log('   请检查ZIP中的download_summary.json查看详细结果')
        resolve()
      })
      
      writer.on('error', (err) => {
        console.log('❌ 错误处理测试失败:', err.message)
        reject(err)
      })
    })
  } catch (error) {
    console.log('❌ 错误处理测试失败:', error.message)
    throw error
  }
}

/**
 * 测试4: 参数验证测试
 */
async function testParameterValidation() {
  console.log('\n=== 测试4: 参数验证测试 ===')
  
  const testCases = [
    {
      name: '空URL数组',
      data: { urls: [] },
      expectError: true
    },
    {
      name: '无URLs参数',
      data: { zipName: 'test.zip' },
      expectError: true
    },
    {
      name: '非数组URLs',
      data: { urls: 'not-an-array' },
      expectError: true
    },
    {
      name: '超过100个URL',
      data: { urls: new Array(101).fill('https://httpbin.org/json') },
      expectError: true
    }
  ]
  
  for (const testCase of testCases) {
    try {
      console.log(`   测试: ${testCase.name}`)
      
      const response = await axios.post(TEST_CONFIG.baseUrl + TEST_CONFIG.endpoint, testCase.data, {
        timeout: 10000
      })
      
      if (testCase.expectError) {
        console.log(`   ❌ 应该返回错误但成功了`)
      } else {
        console.log(`   ✅ 成功`)
      }
    } catch (error) {
      if (testCase.expectError) {
        console.log(`   ✅ 正确返回错误: ${error.response?.data?.message || error.message}`)
      } else {
        console.log(`   ❌ 不应该返回错误: ${error.message}`)
      }
    }
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始批量下载ZIP功能测试...')
  console.log(`测试服务器: ${TEST_CONFIG.baseUrl}`)
  console.log(`输出目录: ${TEST_CONFIG.outputDir}`)
  
  try {
    await testBasicFunctionality()
    await testCustomFilenames()
    await testErrorHandling()
    await testParameterValidation()
    
    console.log('\n🎉 所有测试完成!')
    console.log(`请检查 ${TEST_CONFIG.outputDir} 目录中的下载文件`)
  } catch (error) {
    console.log('\n💥 测试过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testBasicFunctionality,
  testCustomFilenames,
  testErrorHandling,
  testParameterValidation,
  runAllTests
}
