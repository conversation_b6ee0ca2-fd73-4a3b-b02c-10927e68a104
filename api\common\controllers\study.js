const _ = require('lodash');
const client = require('../../common/client');
const enums = require('../../common/enums');
const utils = require('../../common/lib/utils');
const Joi = require('joi');

module.exports = {
  getFilters,
  // getStudyInfo,
  // updatePeriod,
  // updateSubject,
  getInfo,
  updateInfo,
}

const PeriodExamType = {
  '高中': ['同步练习', '月考试卷', '期中试卷', '期末试卷', '单元测试', '开学考试', '高考真卷', '高考模拟', '高考复习', '其他'],
  '初中': ['同步练习', '月考试卷', '期中试卷', '期末试卷', '单元测试', '开学考试', '中考真卷', '中考模拟', '中考复习', '其他'],
  '小学': ['同步练习', '月考试卷', '期中试卷', '期末试卷', '单元测试', '开学考试', '小升初真卷', '小升初模拟', '小升初复习', '其他'],
}
// 获取试题筛选项
async function getFilters(ctx) {
  const user = ctx.state.user;
  // 查询所有的学段信息
  const result = [];
  const periodList = await strapi.query('period').find({pBranch: user.pBranch.id});
  if (!_.size(periodList)) return ctx.wrapper.succ(result);
  const questionFilters = await client.kb.getQuestionFilters();
  for (const period of periodList) {
    const data = {
      period: period.name,
      // exam_type: PeriodExamType[period.name],
      grades: [],
      subjects: []
    };
    const kbPeriod = questionFilters.find(e => e.period === data.period);
    if (_.size(period.subjects)) {
      // 添加科目
      for (const sub of period.subjects) {
        const subject = {subject: sub.name};
        // 增加试题类型
       if (kbPeriod) {
         const kbSubject = kbPeriod.subjects.find(e => e.subject === subject.subject);
         if (kbSubject) {
           subject.exam_type = kbSubject.exam_type;
           subject.type = kbSubject.type;
         }
       }
        // 增加考试类型
        data.subjects.push(subject);
      }
    }
    result.push(data);
  }
  // 获取年级信息
  const gradeGroup = await client.yj.getGradeInfo(user.yjUserInfo.schoolId);
  if (_.size(gradeGroup)) {
    for (const group of gradeGroup) {
      const period = result.find(e => e.period === group.name);
      if (period) period.grades = (group.grades || []).map(e => e.gradeName);
    }
  }
  return ctx.wrapper.succ(result);
}

async function getInfo(ctx) {
  const user = ctx.state.user;
  const doc = await strapi.query(enums.CollectionName.BRANCH_PROPERTY).findOne({pBranch: user.pBranch.id, key: enums.BranchPropertyKey.PERIOD_SUBJECT});
  return ctx.wrapper.succ(_.isEmpty(doc) ? [] : {children: doc.children});
}

const JOI_UPDATE_INFO = Joi.object({
  children: Joi.array().items(Joi.object({
    key: Joi.string().required().valid('period'),
    name: Joi.string().required(),
    children: Joi.array().items(Joi.object({
      key: Joi.string().required().valid('subject'),
      name: Joi.string().required(),
    }))
  }))
});

async function updateInfo(ctx) {
  const {error, value} = JOI_UPDATE_INFO.validate(ctx.request.body);
  if (error) return ctx.wrapper.error('HANDLE_ERROR', `参数错误：${error.message}`);
  const user = ctx.state.user;
  const query = {pBranch: user.pBranch.id, key: enums.BranchPropertyKey.PERIOD_SUBJECT}
  const doc = await strapi.query(enums.CollectionName.BRANCH_PROPERTY).findOne(query);
  if (_.isEmpty(doc)) {
    await strapi.query(enums.CollectionName.BRANCH_PROPERTY).create({
      key: enums.BranchPropertyKey.PERIOD_SUBJECT,
      children: value.children,
      pBranch: user.pBranch.id,
      creator: user.id,
      operator: user.id
    });
  } else {
    await strapi.query(enums.CollectionName.BRANCH_PROPERTY).update(query, {
      children: value.children,
      operator: user.id
    });
  }
  return ctx.wrapper.succ('');
}
