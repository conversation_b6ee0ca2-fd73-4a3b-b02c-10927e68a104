const axios = require('axios');
const URL = require('url');
const qs = require('querystring');
const server = strapi.config.server.aiApi;
const logger = strapi.log;

module.exports = {
  wordParse,
  imagesParse,
}

const options  = {
  timeout: 10 * 60 * 1000
}

/**
 * word试卷解析
 * @param period 学段
 * @param subject 科目
 * @param type 试卷类型
 * @param name 名称
 * @param word_url 文件地址
 * @returns {Promise<any>}
 */
async function wordParse(params) {
  const url = URL.format({
    host: server.url,
    pathname: `/parse`,
  });
  const result = await axios.post(url, {
    username: params.username,
    period: params.period,
    subject: params.subject,
    grade: params.grade,
    files: params.files,
    name: params.name,
    word_url: params.word_url,
    knowledge_predict: true,
    inner_html: false
  }, options);
  if (result.data.code !== 0) {
    logger.error(`试卷解析失败: ${JSON.stringify(result.data)}`);
  }
  return result.data;
}

/**
 * word试卷解析
 * @param files 
 * @returns {Promise<any>}
 */
async function imagesParse(params) {
  const url = URL.format({
    host: server.url,
    pathname: `/examocr`,
  });
  const result = await axios.post(url, {
    username: params.username,
    period: params.period,
    subject: params.subject,
    grade: params.grade,
    files: params.files,
    name: params.name,
    knowledge_predict: true,
    inner_html: false,
  }, options);
  if (result.data.code !== 0) {
    logger.error(`试卷解析失败: ${JSON.stringify(result.data)}`);
  }
  return result.data;
}

