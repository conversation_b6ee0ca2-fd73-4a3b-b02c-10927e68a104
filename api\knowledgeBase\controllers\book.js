'use strict'
const { BranchCurdRouter } = require('accel-utils')
const axios = require('axios')
const _ = require('lodash')
const { ObjectId } = require('mongodb')

const { replaceChapterField, getTreeBulkWriteArray } = require('../services/tree')

const branchCurdRouter = new (class extends BranchCurdRouter {
  async branchUpdate (ctx) {
    const { params, data } = this._parseBranchCtx(ctx)
    const userId = ctx.state.user.id
    if (data.name) {
      const book = await strapi.query('book').findOne({ id: params.id, }, [])
      data.key = `${data.name}-${book.press_version}`
    }
    data.operator = userId
    return super.branchUpdate(ctx)
  }
})('book')

// 获取学校教材树
async function getBookById (ctx) {
  let { id, needKnowledge = true } = ctx.request.query
  const branchId = ctx.state.user.pBranch?.id
  if (!branchId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户异常')
  }
  if (needKnowledge === 'false') needKnowledge = false
  const book = await strapi.query('book').findOne({
    id: id,
    pBranch: branchId,
  }, ['subject', 'period', 'press_version'])
  if (!book) { return ctx.wrapper.error('HANDLE_ERROR', 'book not found') }
  let bookChapters = await strapi.query('book-chapter').find({
    book: id,
    id: { $nin: book.book_chapter },
    _limit: -1,
  }, needKnowledge ? ['knowledges'] : [])
  const bookChapterMap = Object.fromEntries(
    bookChapters.map(value => [value.id, value])
  )

  replaceChapterField(book.children, bookChapterMap, needKnowledge)
  // 更新教材树层级
  // await strapi.query('book').update({ id: id }, { children: book.children })
  const notMatchIds = Object.values(bookChapterMap).filter(e => e.match !== true).map(e => e.id)
  if (notMatchIds.length > 0) {
    console.log(`not match ids ${notMatchIds.length}:`, notMatchIds)
    await strapi.query('book-chapter').model.deleteMany({
      _id: { $in: notMatchIds },
    })
  }
  return ctx.wrapper.succ(book)
}

// 更新学校教材树
async function syncBookById (ctx) {
  let { id, children } = ctx.request.body
  const userId = ctx.state.user.id
  const branchId = ctx.state.user.pBranch?.id
  // 参数检测和数据准备
  if (!branchId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户异常')
  }
  if (!id || !children || !Array.isArray(children)) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  let book = await strapi.query('book').findOne({ id, pBranch: branchId, }, [])
  if (!book) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  const knowledges = await strapi.query('knowledge').find({
    period: book.period,
    subject: book.subject,
    _limit: -1,
  }, [])
  const knowledgeMap = Object.fromEntries(
    knowledges.map(value => [value.name, value])
  )

  // 共性更新数据
  const updateData = {
    period: book.period,
    subject: book.subject,
    press_version: book.press_version,
    book: book.id,
    operator: userId,
    creator: userId,
    pBranch: book.pBranch,
  }
  // 构造章节根路径
  let curPath = `${book.period}-${book.subject}-${book.press_version}-${book.id}`
  book.children = children
  await getTreeBulkWriteArray(book, curPath, updateData, knowledgeMap, 'book', 'book-chapter')
  return ctx.wrapper.succ({})
}


module.exports = {
  getBookById,
  syncBookById,
  ...branchCurdRouter.createHandlers(),
}